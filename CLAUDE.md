# Claude Code 実行プロンプト
ユーザーは音声入力を使用しているため、誤字・脱字については文脈から判断してください。判断がつかないものはユーザーに再度質問してください。
現在の設計思想を理解して正しい作業を心がけてください。
omniSearchを活用してベストプラクティスを確認しながら作業を実施してください。
ユーザーからの指示を適切にMCPのタスクマスターを利用して、タスクとして展開して作業を実施してください。
「おそらく～」「～してみましょう」のような基準で作業をすることは禁止です。

## npm
"sass:lpo:compile": "sass mount_wordpress/wp-content/themes/appmart/assets/css/lpo/lpo.scss mount_wordpress/wp-content/themes/appmart/assets/css/lpo/lpo.css --style=expanded --quiet-deps",

## figmaデザイン
PC版
https://www.figma.com/design/WhDvglvBj5UpTUIhYt2vGr/LPO%E3%83%9A%E3%83%BC%E3%82%B8?node-id=1-59737&t=f1fOGu1Vgb6fB3DA-0

SP版
https://www.figma.com/design/WhDvglvBj5UpTUIhYt2vGr/LPO%E3%83%9A%E3%83%BC%E3%82%B8?node-id=1-88566&t=f1fOGu1Vgb6fB3DA-0
