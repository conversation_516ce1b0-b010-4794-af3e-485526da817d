// ==================================================
// ご支援事例
// ==================================================
.lpo-case {
  @include lpo-section;

  position: relative;
  padding-top: 12rem;
  background-color: $lpo-color-bg-light-grean;

  &::before {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    height: 35rem;
    pointer-events: none;
    content: '';
    background-color: #fff;
    background-image: linear-gradient(to right, #fff, #fff), linear-gradient(to bottom, #fff, #fff);
    background-size: 3rem 3rem;
  }

  &__container {
    @include lpo-container(1080px);
  }

  .lpo-case__title {
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
    align-items: center;
    margin-bottom: 10rem;

    &::before {
      position: absolute;
      top: -180px;
      right: -300px;
      z-index: 0;
      width: 588px;
      height: 443px;
      content: '';
      background-image: url('../../images/lpo/lpo-title-arrow-2.svg');
      background-repeat: no-repeat;
      background-position: center;
      background-size: contain;
    }
  }

  &__content-wrap {
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
    align-items: center;
    width: 100%;
    margin-bottom: 7.2rem;

    &:last-child {
      margin-bottom: 10rem;
    }

    .case-content {
      position: relative;
      display: flex;
      flex-direction: column;
      gap: 16px;
      align-items: center;
      width: 100%;
      padding: 4.8rem 11rem;
      background-color: #fff;
      border-radius: 22px;
      box-shadow: 0 0 24px 0 rgb(0 0 0 / 25%);

      &__title {
        position: absolute;
        top: -30px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 414px;
        height: 60px;
        font-size: 2.4rem;
        font-weight: $lpo-font-weight-bold;
        line-height: 1;
        background-color: #fff;
        border: 4px solid $lpo-color-mint;
        border-radius: 30px;

        &-text {
          font-size: 2.8rem;
          font-weight: $lpo-font-weight-bold;
          line-height: 1;
          color: $lpo-color-mint;
        }
      }

      .case-subtitle-wrap {
        display: flex;
        align-items: center;
        justify-content: flex-start;

        .case-subtitle {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 33px;
          padding: 4px 32px;
          font-family: $lpo-font-family-noto;
          font-size: 1.8rem;
          font-weight: $lpo-font-weight-bold;
          color: $lpo-color-text-black;
          background-color: $lpo-color-bg-gray;

          &::after {
            position: absolute;
            top: 0;
            right: -33px;
            z-index: 0;
            width: 33px;
            height: 33px;
            content: '';
            background: linear-gradient(125deg, $lpo-color-bg-gray 50%, #fff 50%);
          }
        }

        .case-subtitle-text {
          margin-left: 33px;
          font-size: 1.6rem;
          font-weight: $lpo-font-weight-semibold;
          color: $lpo-color-text-black;
        }
      }

      .description-items {
        display: flex;
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
        width: 100%;
      }

      .description-item {
        display: flex;
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
        width: 100%;

        &__title {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          width: 100%;
          font-size: 1.6rem;
          font-weight: $lpo-font-weight-semibold;
          color: $lpo-color-text-black;

          &::before {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 30px;
            margin-right: 2rem;
            font-size: 1.8rem;
            font-weight: $lpo-font-weight-medium;
            color: #fff;
            background-color: $lpo-color-text-light-green;
            border-radius: 50%;
          }

          &--1 {
            &::before {
              content: '1';
            }
          }

          &--2 {
            &::before {
              content: '2';
            }
          }

          &--3 {
            &::before {
              content: '3';
            }
          }
        }

        &__vc-wrap {
          position: relative;
          display: flex;
          width: 100%;
          height: 127px;

          &::before {
            position: absolute;
            top: 0;
            left: 48.5%;
            width: 100%;
            height: 100%;
            content: '';
            background-image: url('../../images/lpo/lpo-vc-arrow.svg');
            background-repeat: no-repeat;
            background-position: center;
            background-size: contain;
            transform: translateX(-50%);
          }

          .vc-item {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;

            &::before {
              position: absolute;
              top: 0;
              width: 100%;
              height: 100%;
              content: '';
              background-repeat: no-repeat;
              background-position: center;
              background-size: contain;
            }

            &.vc-before {
              width: 48.5%;

              &::before {
                background-image: url('../../images/lpo/lpo-vc-before.svg');
              }
            }

            &.vc-after {
              width: 51.5%;

              &::before {
                background-image: url('../../images/lpo/lpo-vc-after.svg');
              }
            }

            &__title {
              &-wrap {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 44px;
              }

              @include lpo-text(1.6rem, $lpo-font-weight-bold);
            }

            &__data {
              &-wrap {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 85px;
              }

              &-item {
                display: flex;
                flex-direction: column;
                gap: 6px;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 100%;

                .vc-data-text {
                  display: flex;
                  gap: 4px;
                  align-items: flex-end;

                  &__sm {
                    @include lpo-text(1.8rem, $lpo-font-weight-medium);
                  }

                  &__md {
                    @include lpo-text(2rem, $lpo-font-weight-medium);
                  }

                  &__lg {
                    @include lpo-text(3rem, $lpo-font-weight-bold);
                  }

                  &__accent {
                    @include lpo-text(2rem, $lpo-font-weight-medium);

                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 32px;
                    padding: 4px 12px;
                    background-color: $lpo-color-text-red;
                    border-radius: 16px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
