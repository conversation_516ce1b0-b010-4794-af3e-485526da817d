// ==================================================
// グローバル設定
// ==================================================

// LPOページ用のhtml font-size設定
html {
  font-size: 62.5% !important; // 10px = 1rem
}

// グローバル設定: lpo-supportページ内の全要素に適用
.lpo-support * {
  box-sizing: border-box;
  font-family: $lpo-font-family-noto;
}

// CTA
.lpo-cta-button-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: transparent;
}

.lpo-cta-button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 362px;
  height: 92px;
  background-color: $lpo-color-text-red;
  border-radius: 46px;
  box-shadow: 0 4px 19px rgb(82 135 121 / 13%);

  &::before {
    position: absolute;
    top: 50%;
    right: 20%;
    width: 9px;
    height: 9px;
    content: '';
    border-top: 3px solid #fff;
    border-right: 3px solid #fff;
    border-radius: 0 6px;
    transform: translateY(-25%) rotate(45deg);
  }

  &__text {
    font-size: 3rem;
    font-weight: $lpo-font-weight-bold;
    line-height: 1;
    color: #fff;
    pointer-events: none;
  }

  &:hover {
    color: $lpo-color-text-red;
    background-color: #fff;
    border: 2px solid $lpo-color-text-red;
    transition: all 0.3s ease;

    .lpo-cta-button__text {
      color: $lpo-color-text-red;
    }

    &::before {
      border-top: 3px solid $lpo-color-text-red;
      border-right: 3px solid $lpo-color-text-red;
    }
  }
}
