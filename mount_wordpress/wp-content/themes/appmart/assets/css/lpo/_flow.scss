// ////////////////////////////
// LPOフロー
// ////////////////////////////
.lpo-flow {
  @include lpo-section($lpo-color-bg-light-gray);

  padding-top: 12rem;

  &__container {
    @include lpo-container(860px);
  }

  .lpo-flow__title {
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
    align-items: center;
    margin-bottom: 6rem;
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: 32px;
    align-items: center;
    width: 100%;
  }

  .flow-step-wrap {
    display: flex;
    flex-direction: column;
    gap: 34px;
    align-items: center;
    width: 100%;
  }

  .flow-step {
    position: relative;
    z-index: 3;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    min-height: 120px;
    padding: 4px 0;
    background-color: #fff;
    box-shadow: 0 4px 11px rgb(0 0 0 / 25%);

    &::before {
      position: absolute;
      bottom: -10px;
      left: 50%;
      z-index: 0;
      width: 20px;
      height: 20px;
      content: '';
      background-color: #fff;
      box-shadow: 0 4px 11px rgb(0 0 0 / 25%);
      transform: translateX(-50%) rotate(45deg) skew(15deg, 15deg);
    }

    &__number {
      flex-shrink: 0;
      padding: 28px 32px;
      font-size: 40px;
      font-weight: 700;
      text-align: center;
      white-space: nowrap;
    }

    &__description {
      display: flex;
      flex: 1;
      flex-direction: column;

      &-title {
        font-size: 24px;
        font-weight: 700;
        line-height: normal;
      }

      &-text {
        font-size: 16px;
        font-weight: 400;
        line-height: normal;
      }
    }

    &__icon {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      justify-content: center;
      width: auto;
      max-width: 140px;
      height: auto;
      max-height: 100%;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
  }
}
