// ==================================================
// ユーティリティクラス
// ==================================================

// 表示制御
.u-pc-only {
  @include lpo-mobile {
    display: none !important;
  }
}

.u-sp-only {
  display: none !important;

  @include lpo-mobile {
    display: block !important;
  }

  &.inline {
    @include lpo-mobile {
      display: inline-flex !important;
    }
  }
}

// テキスト装飾
.u-underline {
  position: relative;
  display: inline-block;

  &::before {
    position: absolute;
    bottom: -4px;
    left: 0;
    z-index: -1;
    width: 100%;
    height: 14px;
    content: '';
    background-color: #fff54b;
    border-radius: 7px;
  }

  &--mint {
    &::before {
      bottom: -4px;
      height: 18px;
      background-color: #b1e2d5;
      border-radius: unset;
    }
  }

  &--none {
    &::before {
      display: none;
    }
  }
}

.u-fs {
  &__2 {
    font-size: 2rem;
  }
}

// JavaScriptで生成される1文字ずつのドット付きテキスト
.u-text-dot-char {
  position: relative;
  display: inline-block;

  &::before {
    position: absolute;
    top: -12px;
    left: 50%;
    width: 8px;
    height: 8px;
    content: '';
    background-color: #fa6b58;
    border-radius: 50%;
    transform: translateX(-50%);
  }
}
