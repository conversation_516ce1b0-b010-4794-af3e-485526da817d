/* stylelint-disable scss/dollar-variable-colon-space-after */
@import 'https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@100;300;400;500;700;900&display=swap';
@import 'https://fonts.googleapis.com/css2?family=Yu+Gothic:wght@300;400;500;700&display=swap';
@media (max-width: 768px) {
  .u-pc-only {
    display: none !important;
  }
}

.u-sp-only {
  display: none !important;
}
@media (max-width: 768px) {
  .u-sp-only {
    display: block !important;
  }
}
@media (max-width: 768px) {
  .u-sp-only.inline {
    display: inline-flex !important;
  }
}

.u-underline {
  position: relative;
  display: inline-block;
}
.u-underline::before {
  position: absolute;
  bottom: -4px;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 14px;
  content: "";
  background-color: #fff54b;
  border-radius: 7px;
}
.u-underline--mint::before {
  bottom: -4px;
  height: 18px;
  background-color: #b1e2d5;
  border-radius: unset;
}
.u-underline--none::before {
  display: none;
}

.u-fs__2 {
  font-size: 2rem;
}

.u-text-dot-char {
  position: relative;
  display: inline-block;
}
.u-text-dot-char::before {
  position: absolute;
  top: -12px;
  left: 50%;
  width: 8px;
  height: 8px;
  content: "";
  background-color: #fa6b58;
  border-radius: 50%;
  transform: translateX(-50%);
}

html {
  font-size: 62.5% !important;
}

.lpo-support * {
  box-sizing: border-box;
  font-family: "Noto Sans JP", helvetica, sans-serif;
}

.lpo-cta-button-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: transparent;
}

.lpo-cta-button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 362px;
  height: 92px;
  background-color: #fa6b58;
  border-radius: 46px;
  box-shadow: 0 4px 19px rgba(82, 135, 121, 0.13);
}
.lpo-cta-button::before {
  position: absolute;
  top: 50%;
  right: 20%;
  width: 9px;
  height: 9px;
  content: "";
  border-top: 3px solid #fff;
  border-right: 3px solid #fff;
  border-radius: 0 6px;
  transform: translateY(-25%) rotate(45deg);
}
.lpo-cta-button__text {
  font-size: 3rem;
  font-weight: 700;
  line-height: 1;
  color: #fff;
  pointer-events: none;
}
.lpo-cta-button:hover {
  color: #fa6b58;
  background-color: #fff;
  border: 2px solid #fa6b58;
  transition: all 0.3s ease;
}
.lpo-cta-button:hover .lpo-cta-button__text {
  color: #fa6b58;
}
.lpo-cta-button:hover::before {
  border-top: 3px solid #fa6b58;
  border-right: 3px solid #fa6b58;
}

.lpo-check {
  width: 100%;
  padding: 60px 0 100px;
  background-color: #fff;
  position: relative;
  background-color: #e0f3ee;
}
@media (max-width: 480px) {
  .lpo-check {
    padding: 80px 0 40px;
  }
}
.lpo-check::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 33rem;
  pointer-events: none;
  content: "";
  background-color: #f9f9f9;
  background-image: linear-gradient(to right, #c0c0c0 0.5px, transparent 0.5px), linear-gradient(to bottom, #c0c0c0 0.5px, transparent 0.5px);
  background-size: 3rem 3rem;
}
.lpo-check::after {
  position: absolute;
  bottom: -2rem;
  left: 50%;
  z-index: 2;
  width: 4rem;
  height: 4rem;
  content: "";
  background-color: #e0f3ee;
  transform: translateX(-50%) rotate(135deg) skew(10deg, 10deg);
}
.lpo-check__container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 1002px;
  padding: 0 16px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .lpo-check__container {
    max-width: 100%;
  }
}
.lpo-check__container .lpo-section-title {
  font-weight: 700;
  line-height: 1;
  color: #333;
}
.lpo-check__container .lpo-section-title.red {
  color: #fa6b58;
}
.lpo-check__container .lpo-section-title.black {
  color: #333;
}
.lpo-check__container .lpo-section-title.medium {
  font-weight: 500;
}
.lpo-check__container .lpo-section-title.xs {
  font-size: 2rem;
}
.lpo-check__container .lpo-section-title.sm {
  font-size: 2.4rem;
}
.lpo-check__container .lpo-section-title.md {
  font-size: 4rem;
}
.lpo-check__container .lpo-section-title.lg {
  font-size: 5rem;
}
.lpo-check__title {
  display: flex;
  flex-direction: column;
  align-items: center;
  line-height: 1;
}
.lpo-check__check-list {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  gap: 0.6rem;
  row-gap: 7rem;
  justify-content: center;
  width: 100%;
  margin-top: 10rem;
}
.lpo-check__check-list::before {
  position: absolute;
  top: -8rem;
  left: 50%;
  display: block;
  width: 6.3px;
  height: 3.6rem;
  content: "";
  background-image: url("../../images/lpo/case-dot.svg");
  background-repeat: no-repeat;
  background-size: contain;
  transform: translateX(-50%);
}
.lpo-check__check-list .check-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: calc(33% - 0.2rem);
  max-height: 15.8rem;
  padding: 5.6rem 0 4.8rem;
  background-color: #fff;
  border: 1px solid #a0c1bb;
  border-bottom: 1px solid #c0c0c0;
  box-shadow: 0 0 14px 0 rgba(125, 200, 182, 0.4);
}
.lpo-check__check-list .check-item::after {
  position: absolute;
  top: -3.4rem;
  left: 50%;
  width: 6.8rem;
  height: 6.8rem;
  content: "";
  background-image: url("../../images/lpo/case-check-icon.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  transform: translateX(-50%);
}
.lpo-check__check-list .check-item__text {
  z-index: 1;
  display: inline-block;
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
}
.lpo-check__check-list .check-item__text.white {
  color: #fff;
}
.lpo-check__check-list .check-item__text.sumikuro {
  color: #5f6061;
}
.lpo-check__check-list .check-item__text.black {
  color: #333;
}
.lpo-check__check-list .check-item__text.gray {
  color: #8b8b8b;
}
.lpo-check__check-list .check-item__text.red {
  color: #fa6b58;
}
.lpo-check__check-list .check-item__text.green {
  color: #3c8b86;
}
.lpo-check__check-list .check-item__text.light-green {
  color: #76aaaa;
}

.lpo-improve {
  width: 100%;
  padding: 60px 0 100px;
  background-color: #fff;
  position: relative;
  padding-bottom: 40px;
  background-color: #e0f3ee;
}
@media (max-width: 480px) {
  .lpo-improve {
    padding: 80px 0 40px;
  }
}
.lpo-improve::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 33rem;
  pointer-events: none;
  content: "";
  background-color: #f9f9f9;
  background-image: linear-gradient(to right, #c0c0c0 0.5px, transparent 0.5px), linear-gradient(to bottom, #c0c0c0 0.5px, transparent 0.5px);
  background-size: 3rem 3rem;
}
.lpo-improve__container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 782px;
  padding: 0 16px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .lpo-improve__container {
    max-width: 100%;
  }
}
.lpo-improve__container .lpo-section-title {
  font-weight: 700;
  line-height: 1;
  color: #333;
}
.lpo-improve__container .lpo-section-title.red {
  color: #fa6b58;
}
.lpo-improve__container .lpo-section-title.black {
  color: #333;
}
.lpo-improve__container .lpo-section-title.medium {
  font-weight: 500;
}
.lpo-improve__container .lpo-section-title.xs {
  font-size: 2rem;
}
.lpo-improve__container .lpo-section-title.sm {
  font-size: 2.4rem;
}
.lpo-improve__container .lpo-section-title.md {
  font-size: 4rem;
}
.lpo-improve__container .lpo-section-title.lg {
  font-size: 5rem;
}
.lpo-improve__container::before {
  position: absolute;
  top: -60px;
  right: 20px;
  z-index: 0;
  width: 522px;
  height: 394px;
  content: "";
  background-image: url("../../images/lpo/lpo-title-arrow-1.svg");
  background-repeat: no-repeat;
  background-position: bottom;
  background-size: 100% 100%;
}
.lpo-improve__title {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: flex-end;
  margin-bottom: 6rem;
}
.lpo-improve__bfaf {
  z-index: 1;
  width: 100%;
}
.lpo-improve__bfaf .tab-wrap {
  display: flex;
  gap: 1.2rem;
  justify-content: center;
}
.lpo-improve__bfaf .tab-wrap .tab__item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  justify-content: center;
  width: 50%;
  width: 300px;
  height: 80px;
  border-radius: 19px 19px 0 0;
}
.lpo-improve__bfaf .tab-wrap .tab__item--left {
  background-color: #e1ede8;
}
.lpo-improve__bfaf .tab-wrap .tab__item--right {
  background-color: #fff;
}
.lpo-improve__bfaf .tab-wrap .tab__item-text {
  z-index: 1;
  display: inline-block;
  font-size: 2.4rem;
  font-weight: 700;
  line-height: 1;
}
.lpo-improve__bfaf .tab-wrap .tab__item-text.white {
  color: #fff;
}
.lpo-improve__bfaf .tab-wrap .tab__item-text.sumikuro {
  color: #5f6061;
}
.lpo-improve__bfaf .tab-wrap .tab__item-text.black {
  color: #333;
}
.lpo-improve__bfaf .tab-wrap .tab__item-text.gray {
  color: #8b8b8b;
}
.lpo-improve__bfaf .tab-wrap .tab__item-text.red {
  color: #fa6b58;
}
.lpo-improve__bfaf .tab-wrap .tab__item-text.green {
  color: #3c8b86;
}
.lpo-improve__bfaf .tab-wrap .tab__item-text.light-green {
  color: #76aaaa;
}
.lpo-improve__bfaf .data-wrap {
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 20px 0;
  border-radius: 26px;
  background: linear-gradient(to right, transparent 50%, #fff 50%), linear-gradient(to bottom, #e1ede8 0%, #fff 70%);
  background-repeat: no-repeat;
  background-position: 0 0, 0 0;
  background-size: 100% 100%, 50% 100%;
}
.lpo-improve__bfaf .data-wrap .data-item {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
  width: 100%;
  max-width: 610px;
  height: 100%;
  padding-bottom: 20px;
  overflow: hidden;
  border: 3px solid #fff;
  border-radius: 16px;
  box-shadow: 0 0 4px 0 #3c8b86;
  background: linear-gradient(to right, transparent 50%, #fff 50%), linear-gradient(to bottom, #e1ede8 0%, #fff 100%);
  background-repeat: no-repeat;
  background-position: 0 0, 0 0;
  background-size: 100% 100%, 50% 100%;
}
.lpo-improve__bfaf .data-wrap .data-item-title {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: 34px;
  padding-left: 60px;
}
.lpo-improve__bfaf .data-wrap .data-item-title::before {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  width: 50%;
  height: 100%;
  content: "";
  background-color: #3c8b86;
}
.lpo-improve__bfaf .data-wrap .data-item-title::after {
  position: absolute;
  top: 0;
  left: 50%;
  z-index: 0;
  width: 34px;
  height: 34px;
  content: "";
  background: linear-gradient(45deg, #3c8b86 50%, #fff 50%);
}
.lpo-improve__bfaf .data-wrap .data-item-title__number {
  position: absolute;
  top: 50%;
  left: 24px;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  font-size: 2rem;
  font-weight: 700;
  color: #3c8b86;
  background-color: #fff;
  border-radius: 50%;
  transform: translateY(-50%);
}
.lpo-improve__bfaf .data-wrap .data-item-title__text {
  z-index: 2;
  z-index: 1;
  display: inline-block;
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
}
.lpo-improve__bfaf .data-wrap .data-item-title__text.white {
  color: #fff;
}
.lpo-improve__bfaf .data-wrap .data-item-title__text.sumikuro {
  color: #5f6061;
}
.lpo-improve__bfaf .data-wrap .data-item-title__text.black {
  color: #333;
}
.lpo-improve__bfaf .data-wrap .data-item-title__text.gray {
  color: #8b8b8b;
}
.lpo-improve__bfaf .data-wrap .data-item-title__text.red {
  color: #fa6b58;
}
.lpo-improve__bfaf .data-wrap .data-item-title__text.green {
  color: #3c8b86;
}
.lpo-improve__bfaf .data-wrap .data-item-title__text.light-green {
  color: #76aaaa;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-image {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-image__1 {
  max-width: 370px;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-image__2 {
  max-width: 370px;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-text {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-text__1 {
  z-index: 1;
  display: inline-block;
  font-size: 2.4rem;
  font-weight: 700;
  line-height: 1;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-text__1.white {
  color: #fff;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-text__1.sumikuro {
  color: #5f6061;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-text__1.black {
  color: #333;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-text__1.gray {
  color: #8b8b8b;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-text__1.red {
  color: #fa6b58;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-text__1.green {
  color: #3c8b86;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-text__1.light-green {
  color: #76aaaa;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap {
  position: relative;
  display: flex;
  width: 100%;
  height: 100%;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio {
  position: absolute;
  top: 50%;
  left: 43%;
  z-index: 1;
  transform: translateY(-50%);
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--lg {
  z-index: 1;
  display: inline-block;
  font-size: 4rem;
  font-weight: 700;
  line-height: 1;
  margin-right: -4px;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--lg.white {
  color: #fff;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--lg.sumikuro {
  color: #5f6061;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--lg.black {
  color: #333;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--lg.gray {
  color: #8b8b8b;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--lg.red {
  color: #fa6b58;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--lg.green {
  color: #3c8b86;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--lg.light-green {
  color: #76aaaa;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--sm {
  z-index: 1;
  display: inline-block;
  font-size: 2.4rem;
  font-weight: 700;
  line-height: 1;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--sm.white {
  color: #fff;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--sm.sumikuro {
  color: #5f6061;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--sm.black {
  color: #333;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--sm.gray {
  color: #8b8b8b;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--sm.red {
  color: #fa6b58;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--sm.green {
  color: #3c8b86;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--sm.light-green {
  color: #76aaaa;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio::before {
  position: absolute;
  top: 0;
  right: -55px;
  z-index: -1;
  width: 88px;
  height: 44px;
  content: "";
  background-image: url("../../images/lpo/lpo-ratio-arrow.svg");
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
  justify-content: center;
  width: 50%;
  padding-top: 22px;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content--left {
  padding-right: 48px;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content--right {
  padding-left: 48px;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 192px;
  height: 38px;
  border-radius: 40px;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user::before {
  position: absolute;
  left: 50%;
  content: "";
  background-repeat: no-repeat;
  background-size: contain;
  transform: translateX(-50%);
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__2-1 {
  background-color: #3c8b86;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__2-1::before {
  top: -24px;
  width: 62px;
  height: 28px;
  background-image: url("../../images/lpo/lpo-bfaf-2-1.svg");
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__2-2 {
  background-color: #fff;
  border: 3px solid #fa6b58;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__2-2::before {
  top: -51px;
  width: 156px;
  height: 50px;
  background-image: url("../../images/lpo/lpo-bfaf-2-2.svg");
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__3-1 {
  background-color: #3c8b86;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__3-1::before {
  top: -22px;
  width: 39px;
  height: 23px;
  background-image: url("../../images/lpo/lpo-bfaf-3-1.svg");
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__3-2 {
  background-color: #fff;
  border: 3px solid #fa6b58;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__3-2::before {
  top: -51px;
  width: 90px;
  height: 50px;
  background-image: url("../../images/lpo/lpo-bfaf-3-2.svg");
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__text {
  z-index: 1;
  display: inline-block;
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__text.white {
  color: #fff;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__text.sumikuro {
  color: #5f6061;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__text.black {
  color: #333;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__text.gray {
  color: #8b8b8b;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__text.red {
  color: #fa6b58;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__text.green {
  color: #3c8b86;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__text.light-green {
  color: #76aaaa;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content__text {
  z-index: 1;
  display: inline-block;
  font-size: 1.2rem;
  font-weight: 700;
  line-height: 1;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content__text.white {
  color: #fff;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content__text.sumikuro {
  color: #5f6061;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content__text.black {
  color: #333;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content__text.gray {
  color: #8b8b8b;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content__text.red {
  color: #fa6b58;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content__text.green {
  color: #3c8b86;
}
.lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content__text.light-green {
  color: #76aaaa;
}
.lpo-improve__message {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  margin-top: 24px;
}
.lpo-improve__message::before {
  position: absolute;
  top: 8px;
  left: 50%;
  z-index: 0;
  width: 120%;
  height: 120%;
  content: "";
  background-image: url("../../images/lpo/lpo-bfaf-message-bg.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
  transform: translateX(-50%);
}
.lpo-improve__message.no-bg::before {
  display: none;
}
.lpo-improve__message-text--sm {
  z-index: 1;
  display: inline-block;
  font-size: 1.2rem;
  font-weight: 400;
  line-height: 1;
}
.lpo-improve__message-text--sm.white {
  color: #fff;
}
.lpo-improve__message-text--sm.sumikuro {
  color: #5f6061;
}
.lpo-improve__message-text--sm.black {
  color: #333;
}
.lpo-improve__message-text--sm.gray {
  color: #8b8b8b;
}
.lpo-improve__message-text--sm.red {
  color: #fa6b58;
}
.lpo-improve__message-text--sm.green {
  color: #3c8b86;
}
.lpo-improve__message-text--sm.light-green {
  color: #76aaaa;
}
.lpo-improve__message-text--md {
  z-index: 1;
  display: inline-block;
  font-size: 2.4rem;
  font-weight: 700;
  line-height: 1;
}
.lpo-improve__message-text--md.white {
  color: #fff;
}
.lpo-improve__message-text--md.sumikuro {
  color: #5f6061;
}
.lpo-improve__message-text--md.black {
  color: #333;
}
.lpo-improve__message-text--md.gray {
  color: #8b8b8b;
}
.lpo-improve__message-text--md.red {
  color: #fa6b58;
}
.lpo-improve__message-text--md.green {
  color: #3c8b86;
}
.lpo-improve__message-text--md.light-green {
  color: #76aaaa;
}
.lpo-improve__message-text--lg {
  z-index: 1;
  display: inline-block;
  font-size: 3rem;
  font-weight: 700;
  line-height: 1;
}
.lpo-improve__message-text--lg.white {
  color: #fff;
}
.lpo-improve__message-text--lg.sumikuro {
  color: #5f6061;
}
.lpo-improve__message-text--lg.black {
  color: #333;
}
.lpo-improve__message-text--lg.gray {
  color: #8b8b8b;
}
.lpo-improve__message-text--lg.red {
  color: #fa6b58;
}
.lpo-improve__message-text--lg.green {
  color: #3c8b86;
}
.lpo-improve__message-text--lg.light-green {
  color: #76aaaa;
}

.lpo-service {
  width: 100%;
  padding: 60px 0 100px;
  background-color: #fff;
  position: relative;
  padding-bottom: 10rem;
  background-color: #e0f3ee;
}
@media (max-width: 480px) {
  .lpo-service {
    padding: 80px 0 40px;
  }
}
.lpo-service::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 18rem;
  pointer-events: none;
  content: "";
  background-color: #fff;
  background-image: linear-gradient(to right, #fff, #fff), linear-gradient(to bottom, #fff, #fff);
  background-size: 3rem 3rem;
}
.lpo-service__container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 892px;
  padding: 0 16px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .lpo-service__container {
    max-width: 100%;
  }
}
.lpo-service__container .lpo-section-title {
  font-weight: 700;
  line-height: 1;
  color: #333;
}
.lpo-service__container .lpo-section-title.red {
  color: #fa6b58;
}
.lpo-service__container .lpo-section-title.black {
  color: #333;
}
.lpo-service__container .lpo-section-title.medium {
  font-weight: 500;
}
.lpo-service__container .lpo-section-title.xs {
  font-size: 2rem;
}
.lpo-service__container .lpo-section-title.sm {
  font-size: 2.4rem;
}
.lpo-service__container .lpo-section-title.md {
  font-size: 4rem;
}
.lpo-service__container .lpo-section-title.lg {
  font-size: 5rem;
}
.lpo-service .lpo-section__title {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
  margin-bottom: 5rem;
}
.lpo-service .service-items {
  display: flex;
  flex-direction: column;
  gap: 40px;
  align-items: center;
  width: 100%;
  padding-top: 4rem;
}
.lpo-service .service-items:last-child {
  margin-bottom: 10rem;
}
.lpo-service .service-items .service-item {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
}
.lpo-service .service-items .service-item__title-wrap {
  display: flex;
  gap: 40px;
  align-items: center;
  width: 100%;
  height: 55px;
  padding: 4px;
  background-color: #fff;
  border-radius: 9px;
}
.lpo-service .service-items .service-item__title-wrap::before {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 47px;
  height: 47px;
  font-size: 2.4rem;
  font-weight: 700;
  line-height: 1;
  color: #fff;
  text-align: center;
  background-color: #3ab795;
  border-radius: 9px;
}
.lpo-service .service-items .service-item__title-wrap--1::before {
  content: "01";
}
.lpo-service .service-items .service-item__title-wrap--2::before {
  content: "02";
}
.lpo-service .service-items .service-item__title-wrap--3::before {
  content: "03";
}
.lpo-service .service-items .service-item__title-wrap--4::before {
  content: "04";
}
.lpo-service .service-items .service-item__title-wrap .service-item__title {
  z-index: 1;
  display: inline-block;
  font-size: 2.4rem;
  font-weight: 500;
  line-height: 1;
}
.lpo-service .service-items .service-item__title-wrap .service-item__title.white {
  color: #fff;
}
.lpo-service .service-items .service-item__title-wrap .service-item__title.sumikuro {
  color: #5f6061;
}
.lpo-service .service-items .service-item__title-wrap .service-item__title.black {
  color: #333;
}
.lpo-service .service-items .service-item__title-wrap .service-item__title.gray {
  color: #8b8b8b;
}
.lpo-service .service-items .service-item__title-wrap .service-item__title.red {
  color: #fa6b58;
}
.lpo-service .service-items .service-item__title-wrap .service-item__title.green {
  color: #3c8b86;
}
.lpo-service .service-items .service-item__title-wrap .service-item__title.light-green {
  color: #76aaaa;
}
.lpo-service .service-items .service-item__description {
  z-index: 1;
  display: inline-block;
  font-size: 1.6rem;
  font-weight: 500;
  line-height: 1;
  padding-left: 60px;
  color: #333;
}
.lpo-service .service-items .service-item__description.white {
  color: #fff;
}
.lpo-service .service-items .service-item__description.sumikuro {
  color: #5f6061;
}
.lpo-service .service-items .service-item__description.black {
  color: #333;
}
.lpo-service .service-items .service-item__description.gray {
  color: #8b8b8b;
}
.lpo-service .service-items .service-item__description.red {
  color: #fa6b58;
}
.lpo-service .service-items .service-item__description.green {
  color: #3c8b86;
}
.lpo-service .service-items .service-item__description.light-green {
  color: #76aaaa;
}
.lpo-service .service-items .service-item__image {
  display: flex;
  gap: 22px;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  padding: 0 60px;
}
.lpo-service .service-items .service-item__image .service-image-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50%;
  padding: 4px;
  background-color: #fff;
  border: 2px solid #a0c1bb;
  border-radius: 9px;
  box-shadow: 0 0 14px 0 rgba(125, 200, 182, 0.8);
}
.lpo-service .service-items .service-item__image .service-image-wrap--1 {
  width: 100%;
}
.lpo-service .service-items .service-item__image .service-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.lpo-case {
  width: 100%;
  padding: 60px 0 100px;
  background-color: #fff;
  position: relative;
  padding-top: 12rem;
  background-color: #e0f3ee;
}
@media (max-width: 480px) {
  .lpo-case {
    padding: 80px 0 40px;
  }
}
.lpo-case::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 35rem;
  pointer-events: none;
  content: "";
  background-color: #fff;
  background-image: linear-gradient(to right, #fff, #fff), linear-gradient(to bottom, #fff, #fff);
  background-size: 3rem 3rem;
}
.lpo-case__container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 1112px;
  padding: 0 16px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .lpo-case__container {
    max-width: 100%;
  }
}
.lpo-case__container .lpo-section-title {
  font-weight: 700;
  line-height: 1;
  color: #333;
}
.lpo-case__container .lpo-section-title.red {
  color: #fa6b58;
}
.lpo-case__container .lpo-section-title.black {
  color: #333;
}
.lpo-case__container .lpo-section-title.medium {
  font-weight: 500;
}
.lpo-case__container .lpo-section-title.xs {
  font-size: 2rem;
}
.lpo-case__container .lpo-section-title.sm {
  font-size: 2.4rem;
}
.lpo-case__container .lpo-section-title.md {
  font-size: 4rem;
}
.lpo-case__container .lpo-section-title.lg {
  font-size: 5rem;
}
.lpo-case .lpo-case__title {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
  margin-bottom: 10rem;
}
.lpo-case .lpo-case__title::before {
  position: absolute;
  top: -180px;
  right: -300px;
  z-index: 0;
  width: 588px;
  height: 443px;
  content: "";
  background-image: url("../../images/lpo/lpo-title-arrow-2.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
.lpo-case__content-wrap {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
  width: 100%;
  margin-bottom: 7.2rem;
}
.lpo-case__content-wrap:last-child {
  margin-bottom: 10rem;
}
.lpo-case__content-wrap .case-content {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
  width: 100%;
  padding: 4.8rem 11rem;
  background-color: #fff;
  border-radius: 22px;
  box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.25);
}
.lpo-case__content-wrap .case-content__title {
  position: absolute;
  top: -30px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 414px;
  height: 60px;
  font-size: 2.4rem;
  font-weight: 700;
  line-height: 1;
  background-color: #fff;
  border: 4px solid #3ab795;
  border-radius: 30px;
}
.lpo-case__content-wrap .case-content__title-text {
  font-size: 2.8rem;
  font-weight: 700;
  line-height: 1;
  color: #3ab795;
}
.lpo-case__content-wrap .case-content .case-subtitle-wrap {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.lpo-case__content-wrap .case-content .case-subtitle-wrap .case-subtitle {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 33px;
  padding: 4px 32px;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 1.8rem;
  font-weight: 700;
  color: #333;
  background-color: #d9d9d9;
}
.lpo-case__content-wrap .case-content .case-subtitle-wrap .case-subtitle::after {
  position: absolute;
  top: 0;
  right: -33px;
  z-index: 0;
  width: 33px;
  height: 33px;
  content: "";
  background: linear-gradient(125deg, #d9d9d9 50%, #fff 50%);
}
.lpo-case__content-wrap .case-content .case-subtitle-wrap .case-subtitle-text {
  margin-left: 33px;
  font-size: 1.6rem;
  font-weight: 600;
  color: #333;
}
.lpo-case__content-wrap .case-content .description-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: flex-start;
  width: 100%;
}
.lpo-case__content-wrap .case-content .description-item {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: flex-start;
  width: 100%;
}
.lpo-case__content-wrap .case-content .description-item__title {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  font-size: 1.6rem;
  font-weight: 600;
  color: #333;
}
.lpo-case__content-wrap .case-content .description-item__title::before {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  margin-right: 2rem;
  font-size: 1.8rem;
  font-weight: 500;
  color: #fff;
  background-color: #76aaaa;
  border-radius: 50%;
}
.lpo-case__content-wrap .case-content .description-item__title--1::before {
  content: "1";
}
.lpo-case__content-wrap .case-content .description-item__title--2::before {
  content: "2";
}
.lpo-case__content-wrap .case-content .description-item__title--3::before {
  content: "3";
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap {
  position: relative;
  display: flex;
  width: 100%;
  height: 127px;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap::before {
  position: absolute;
  top: 0;
  left: 48.5%;
  width: 100%;
  height: 100%;
  content: "";
  background-image: url("../../images/lpo/lpo-vc-arrow.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  transform: translateX(-50%);
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item::before {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  content: "";
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item.vc-before {
  width: 48.5%;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item.vc-before::before {
  background-image: url("../../images/lpo/lpo-vc-before.svg");
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item.vc-after {
  width: 51.5%;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item.vc-after::before {
  background-image: url("../../images/lpo/lpo-vc-after.svg");
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__title {
  z-index: 1;
  display: inline-block;
  font-size: 1.6rem;
  font-weight: 700;
  line-height: 1;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__title-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__title.white {
  color: #fff;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__title.sumikuro {
  color: #5f6061;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__title.black {
  color: #333;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__title.gray {
  color: #8b8b8b;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__title.red {
  color: #fa6b58;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__title.green {
  color: #3c8b86;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__title.light-green {
  color: #76aaaa;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 85px;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text {
  display: flex;
  gap: 4px;
  align-items: flex-end;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__sm {
  z-index: 1;
  display: inline-block;
  font-size: 1.8rem;
  font-weight: 500;
  line-height: 1;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__sm.white {
  color: #fff;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__sm.sumikuro {
  color: #5f6061;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__sm.black {
  color: #333;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__sm.gray {
  color: #8b8b8b;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__sm.red {
  color: #fa6b58;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__sm.green {
  color: #3c8b86;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__sm.light-green {
  color: #76aaaa;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__md {
  z-index: 1;
  display: inline-block;
  font-size: 2rem;
  font-weight: 500;
  line-height: 1;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__md.white {
  color: #fff;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__md.sumikuro {
  color: #5f6061;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__md.black {
  color: #333;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__md.gray {
  color: #8b8b8b;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__md.red {
  color: #fa6b58;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__md.green {
  color: #3c8b86;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__md.light-green {
  color: #76aaaa;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__lg {
  z-index: 1;
  display: inline-block;
  font-size: 3rem;
  font-weight: 700;
  line-height: 1;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__lg.white {
  color: #fff;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__lg.sumikuro {
  color: #5f6061;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__lg.black {
  color: #333;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__lg.gray {
  color: #8b8b8b;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__lg.red {
  color: #fa6b58;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__lg.green {
  color: #3c8b86;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__lg.light-green {
  color: #76aaaa;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__accent {
  z-index: 1;
  display: inline-block;
  font-size: 2rem;
  font-weight: 500;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding: 4px 12px;
  background-color: #fa6b58;
  border-radius: 16px;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__accent.white {
  color: #fff;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__accent.sumikuro {
  color: #5f6061;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__accent.black {
  color: #333;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__accent.gray {
  color: #8b8b8b;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__accent.red {
  color: #fa6b58;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__accent.green {
  color: #3c8b86;
}
.lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__accent.light-green {
  color: #76aaaa;
}

.lpo-flow {
  width: 100%;
  padding: 60px 0 100px;
  background-color: #f9f9f9;
  padding-top: 12rem;
}
@media (max-width: 480px) {
  .lpo-flow {
    padding: 80px 0 40px;
  }
}
.lpo-flow__container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 892px;
  padding: 0 16px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .lpo-flow__container {
    max-width: 100%;
  }
}
.lpo-flow__container .lpo-section-title {
  font-weight: 700;
  line-height: 1;
  color: #333;
}
.lpo-flow__container .lpo-section-title.red {
  color: #fa6b58;
}
.lpo-flow__container .lpo-section-title.black {
  color: #333;
}
.lpo-flow__container .lpo-section-title.medium {
  font-weight: 500;
}
.lpo-flow__container .lpo-section-title.xs {
  font-size: 2rem;
}
.lpo-flow__container .lpo-section-title.sm {
  font-size: 2.4rem;
}
.lpo-flow__container .lpo-section-title.md {
  font-size: 4rem;
}
.lpo-flow__container .lpo-section-title.lg {
  font-size: 5rem;
}
.lpo-flow .lpo-flow__title {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
  margin-bottom: 6rem;
}
.lpo-flow__content {
  display: flex;
  flex-direction: column;
  gap: 32px;
  align-items: center;
  width: 100%;
}
.lpo-flow .flow-step-wrap {
  display: flex;
  flex-direction: column;
  gap: 34px;
  align-items: center;
  width: 100%;
}
.lpo-flow .flow-step {
  position: relative;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  min-height: 120px;
  padding: 4px 0;
  background-color: #fff;
  box-shadow: 0 4px 11px rgba(0, 0, 0, 0.25);
}
.lpo-flow .flow-step::before {
  position: absolute;
  bottom: -10px;
  left: 50%;
  z-index: -1;
  width: 20px;
  height: 20px;
  content: "";
  background-color: #fff;
  box-shadow: 0 4px 11px rgba(0, 0, 0, 0.25);
  transform: translateX(-50%) rotate(45deg) skew(15deg, 15deg);
}
.lpo-flow .flow-step__number {
  flex-shrink: 0;
  padding: 28px 32px;
  font-size: 40px;
  font-weight: 700;
  text-align: center;
  white-space: nowrap;
}
.lpo-flow .flow-step__description {
  display: flex;
  flex: 1;
  flex-direction: column;
}
.lpo-flow .flow-step__description-title {
  font-size: 24px;
  font-weight: 700;
  line-height: normal;
}
.lpo-flow .flow-step__description-text {
  font-size: 16px;
  font-weight: 400;
  line-height: normal;
}
.lpo-flow .flow-step__icon {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: auto;
  max-width: 140px;
  height: auto;
  max-height: 100%;
}
.lpo-flow .flow-step__icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/*# sourceMappingURL=lpo.css.map */
