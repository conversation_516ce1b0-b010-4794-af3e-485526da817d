/* Error: unmatched "}".
 *     ,
 * 235 |   }
 *     |   ^
 *     '
 *   mount_wordpress/wp-content/themes/appmart/assets/css/owned-media/_fv.scss 235:3  @import
 *   mount_wordpress/wp-content/themes/appmart/assets/css/owned-media.scss 22:9       root stylesheet */

body::before {
  font-family: "Source Code Pro", "SF Mono", Monaco, Inconsolata, "Fira Mono",
      "Droid Sans Mono", monospace, monospace;
  white-space: pre;
  display: block;
  padding: 1em;
  margin-bottom: 1em;
  border-bottom: 2px solid black;
  content: 'Error: unmatched "}".\a     \2577 \a 235 \2502    }\a     \2502    ^\a     \2575 \a   mount_wordpress/wp-content/themes/appmart/assets/css/owned-media/_fv.scss 235:3  @import\a   mount_wordpress/wp-content/themes/appmart/assets/css/owned-media.scss 22:9       root stylesheet';
}
