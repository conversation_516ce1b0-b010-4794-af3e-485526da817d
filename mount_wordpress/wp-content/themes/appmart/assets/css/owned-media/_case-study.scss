// ////////////////////////////
// ご支援事例
// ////////////////////////////
.owned-media-case-study {
  @include owned-media-section($owned-media-bg-color);
  @include owned-media-section-bg-gradient($owned-media-bg-color, $owned-media-mint-light, 50%);

  &__container {
    @include owned-media-container;

    max-width: 780px;
  }

  @include section-header;

  .owned-media-case-study__header {
    margin-bottom: 28px;

    .owned-media-case-study__title {
      // font-size: 48px;
    }
  }

  &__content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  &__service-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }

  &__service-title {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 50px;
    padding: 12px 24px;
    margin: 0 auto 16px;
    background-color: $owned-media-primary-color;
  }

  &__service-category,
  &__service-name {
    font-family: $owned-media-font-family-noto;
    font-size: 24px;
    font-weight: 700;
    line-height: $owned-media-case-study-title-line-height;
    color: $owned-media-case-study-title-text;
    letter-spacing: $owned-media-case-study-title-letter-spacing;
    white-space: nowrap;
  }

  &__service-description {
    margin-bottom: 12px;
    font-family: $owned-media-font-family-noto;
    font-size: 24px;
    font-weight: 500;
    line-height: $owned-media-case-study-description-line-height;
    color: $owned-media-case-study-description-text;
    text-align: center;
    letter-spacing: $owned-media-case-study-description-letter-spacing;
  }

  &__service-period {
    font-family: $owned-media-font-family-noto;
    font-size: 16px;
    font-weight: 500;
    line-height: $owned-media-case-study-period-line-height;
    color: $owned-media-case-study-period-text;
    text-align: center;
    letter-spacing: $owned-media-case-study-period-letter-spacing;
  }

  &__results-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    padding: 22px;
    padding-top: 60px;
    margin: 0 auto;
    margin-top: $owned-media-case-study-container-margin-top;
    background-color: $owned-media-white;
    border: $owned-media-case-study-container-border-width solid
      $owned-media-case-study-container-border;
    border-radius: $owned-media-case-study-container-border-radius;
    box-shadow: 0 0 15px 0 $owned-media-case-study-container-shadow;

    .result-wrapper {
      position: absolute;
      top: 18px;
      left: 0;
      z-index: 1;
      width: 100%;

      .result-inner {
        display: flex;
        align-items: flex-end;
        justify-content: center;
        width: 100%;
        margin-bottom: 6px;

        &__main {
          font-size: 18px;
          font-weight: 500;
          color: $owned-media-case-study-main-text;
          white-space: nowrap;
        }

        &__accent {
          font-size: 18px;
          font-weight: 900;
          color: $owned-media-case-study-main-text;
          white-space: nowrap;
        }

        &__strong {
          font-size: 32px;
          font-weight: 900;
          line-height: 0.9;
          color: $owned-media-case-study-strong-text;
          white-space: nowrap;
        }
      }
    }

    .result-inner__description {
      display: flex;

      .result-container {
        position: absolute;
        top: 160px;
        left: 80px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;
        width: fit-content;
        padding: 8px 12px;
        border: $owned-media-case-study-result-container-border-width solid $owned-media-mint;
        border-radius: $owned-media-case-study-result-container-border-radius;

        &-top {
          position: relative;
          z-index: 5;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          width: fit-content;
          height: 100%;
          padding-bottom: 6px;
          margin-bottom: $owned-media-case-study-result-container-top-margin-bottom;
          border-bottom: $owned-media-case-study-result-container-divider-border-width solid
            $owned-media-mint;

          &::after {
            position: absolute;
            bottom: -16px;
            left: 50%;
            z-index: -1;
            width: 12px;
            height: 12px;
            content: '';
            background-color: $owned-media-white;
            border-bottom: $owned-media-case-study-result-container-divider-border-width solid
              $owned-media-mint;
            transform: translateY(-50%) rotate(45deg);
          }

          &__text {
            font-size: $owned-media-case-study-result-text-font-size;
            font-weight: 700;
            color: $owned-media-mint;
            white-space: nowrap;

            &-main {
              font-size: 20px;
            }
          }
        }

        &-bottom {
          position: relative;
          z-index: 1;
          display: flex;
          flex-flow: row nowrap;
          align-items: flex-end;
          width: 100%;

          @include owned-media-mobile {
            flex-direction: column;
            gap: 8px;
            align-items: flex-start;
          }

          &__line {
            display: flex;
            gap: 4px;
            align-items: baseline;
          }

          &-line-container {
            display: inline-flex;
            flex-wrap: nowrap;
            align-items: flex-end;
            justify-content: flex-start;
            width: 100%;
            line-height: 1;
          }

          &__text {
            display: flex;
            align-items: flex-end;
            height: $owned-media-case-study-result-bottom-height;
            font-weight: 700;
            color: $owned-media-mint;
            white-space: nowrap;

            &.normal {
              font-size: 20px;
            }

            &.small {
              font-size: $owned-media-case-study-result-small-font-size;
            }

            &.strong {
              font-size: 40px;

              .big {
                font-size: 24px;
              }
            }

            &.design {
              position: relative;
              top: -16px;
              display: inline-flex;
              align-items: center;
              justify-content: center;
              width: 34px;
              min-width: 34px;
              height: 34px;
              padding: 0;
              background-color: $owned-media-mint;
              border-radius: 50%;

              &::before {
                position: absolute;
                bottom: 0;
                left: 50%;
                z-index: -1;
                width: 12px;
                height: 12px;
                content: '';
                background-color: $owned-media-mint;
                transform: translateX(-50%) rotate(45deg) skew(20deg, 20deg);
              }

              & > .text-md {
                font-size: 10px;
                color: $owned-media-white;
              }

              & > .text-sm {
                font-size: $owned-media-case-study-design-text-md-font-size;
                color: $owned-media-white;
              }
            }
          }
        }
      }
    }

    .result-graph {
      width: 100%;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
  }

  // レスポンシブ対応
  @include media-breakpoint-down(md) {
    padding: $owned-media-case-study-mobile-padding-top 0 300px;

    @include owned-media-section-bg-gradient($owned-media-bg-color, $owned-media-mint-light, 530px);

    &__container {
      padding: unset;
      padding: 0 15px;
    }

    &__header {
      margin-bottom: $owned-media-case-study-mobile-header-margin-bottom;
    }

    &__service-title {
      flex-direction: row;
      gap: 0;
      width: 100%;
      height: auto;
      padding: $owned-media-case-study-mobile-title-padding;
      margin: 0 auto $owned-media-case-study-mobile-title-margin-bottom;
    }

    &__service-category,
    &__service-name {
      font-size: $owned-media-case-study-mobile-title-font-size;
      line-height: $owned-media-case-study-mobile-title-line-height;
      white-space: nowrap;
    }

    &__service-description {
      margin-bottom: $owned-media-case-study-mobile-description-margin-bottom;
      font-size: 24px;
      line-height: 1.4;
    }

    &__service-period {
      font-size: clamp(16px, 4vw, 20px);
      line-height: 1.4;
    }

    &__content {
      align-items: center;
    }

    &__results-container {
      position: relative;
      max-width: $owned-media-case-study-mobile-container-max-width;
      height: $owned-media-case-study-mobile-container-height;
      padding: $owned-media-case-study-mobile-container-padding;
      margin-top: $owned-media-case-study-mobile-container-margin-top;
      border: $owned-media-case-study-mobile-container-border-width solid $owned-media-mint;
      border-width: $owned-media-case-study-mobile-container-border-width;
      border-radius: $owned-media-case-study-mobile-container-border-radius;
      box-shadow: 0 0 4px 0 $owned-media-case-study-mobile-container-shadow;

      .result-wrapper {
        position: absolute;
        top: $owned-media-case-study-mobile-wrapper-top;
        left: 0;
        z-index: 3;
        width: 100%;
        padding: $owned-media-case-study-mobile-wrapper-padding;
        background-color: $owned-media-white;
        border: $owned-media-case-study-mobile-wrapper-border-width solid $owned-media-mint-light;
        border-radius: $owned-media-case-study-mobile-wrapper-border-radius;
        box-shadow: 0 0 8px 0 $owned-media-case-study-mobile-wrapper-shadow;

        &::after {
          position: absolute;
          bottom: -20px;
          left: $owned-media-case-study-mobile-wrapper-divider-left;
          z-index: 0;
          width: $owned-media-case-study-mobile-wrapper-divider-size;
          height: $owned-media-case-study-mobile-wrapper-divider-size;
          content: '';
          background-color: $owned-media-white;
          transform: translateY(-50%) rotate(45deg) skew(30deg, 30deg);
        }

        .result-inner {
          display: flex;
          flex-wrap: wrap;
          align-items: flex-end;
          justify-content: center;
          line-height: 1.2;

          &__main,
          &__accent {
            font-size: $owned-media-case-study-mobile-result-font-size;
            text-align: center;
            white-space: nowrap;
          }

          &__strong {
            font-size: $owned-media-case-study-mobile-result-strong-font-size;
            text-align: center;
            white-space: nowrap;
          }

          &__break {
            flex-basis: 100%;
            height: 0;
          }
        }
      }

      .result-inner__description {
        width: 100%;
        height: $owned-media-case-study-mobile-description-height;

        .result-container {
          position: absolute;
          top: $owned-media-case-study-mobile-result-container-top;
          left: 0;
          z-index: 3;
          align-items: center;
          justify-content: flex-start;
          width: 100%;
          height: $owned-media-case-study-mobile-result-container-height;
          height: auto;
          padding: $owned-media-case-study-mobile-result-container-padding;
          margin-top: 22px;
          background-color: $owned-media-white;
          border-width: $owned-media-case-study-mobile-result-container-border-width;
          border-radius: $owned-media-case-study-mobile-result-container-border-radius;

          &::before {
            position: absolute;
            top: -12px;
            left: 30%;
            z-index: 5;
            width: 24px;
            height: 24px;
            content: '';
            background-color: $owned-media-white;
            border-top: 1px solid $owned-media-mint;
            border-left: 1px solid $owned-media-mint;
            transform: translateX(-50%) rotate(45deg) skew(20deg, 20deg);
          }

          &-top {
            align-items: flex-start;
            height: unset;
            padding-bottom: $owned-media-case-study-mobile-result-container-top-padding-bottom;

            &__text {
              font-size: $owned-media-case-study-mobile-result-container-text-font-size;
            }
          }

          &-bottom {
            display: flex;
            flex-direction: column;
            gap: $owned-media-case-study-mobile-result-container-gap;
            align-items: flex-start;
            height: auto;
            margin-top: 16px;

            &__line {
              display: flex;
              align-items: flex-end;
              justify-content: center;
              width: 100%;
              line-height: 1;
            }

            &-sp-line {
              display: inline-flex;
              align-items: flex-end;
              line-height: 1;

              &.center {
                align-items: center;
              }
            }

            &-sp-line-container {
              display: flex;
              align-items: flex-end;
              justify-content: center;
              width: 100%;
              line-height: 1;
            }

            &__text {
              display: inline-flex;
              align-items: flex-end;
              line-height: 1;

              // フォントサイズの調整
              &.normal {
                font-size: $owned-media-case-study-mobile-result-normal-font-size;
              }

              &.small {
                font-size: $owned-media-case-study-mobile-result-small-font-size;
              }

              &.strong {
                position: relative;
                bottom: -4px;
                font-size: 53px;
                line-height: 1;

                .big {
                  position: relative;
                  bottom: 0;
                  font-size: 30px;
                }
              }

              &.strong-design {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                width: 76px;
                height: 76px;
                padding: 0;
                padding: 8px;
                margin: 0 4px;
                font-size: 53px;
                font-weight: 700;
                color: #fff;
                background-color: $owned-media-mint;
                border-radius: 10px;
              }

              &.small-design {
                display: inline-flex;
                align-items: flex-end;
                height: 80%;
                font-size: 30px;
                color: $owned-media-white;
              }

              &.design {
                position: unset;
                top: unset;
                bottom: unset;
                left: unset;

                .text-lg {
                  font-size: $owned-media-case-study-mobile-design-text-lg-font-size;
                  color: $owned-media-white;
                }

                .text-md {
                  font-size: $owned-media-case-study-mobile-design-text-md-font-size;
                  color: $owned-media-white;
                }
              }
            }
          }
        }
      }

      .result-graph {
        margin-top: 0;

        img {
        }
      }
    }
  }
}
