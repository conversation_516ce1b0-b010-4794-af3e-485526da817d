// ========================================
// CTAボタンセクション
// ========================================
.owned-media-cta {
  position: relative;
  width: 100%;
  background-color: $owned-media-base-color;

  &__container {
    @include owned-media-container;

    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 221px;
    padding: 35px 0;
  }

  &__title-wrapper {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 240px;
    height: 29px;
  }

  &__title {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 187.61px;
    height: 29px;
    margin: 0;
    font-family: $owned-media-font-family-noto;
    font-size: 24px;
    font-weight: 700;
    line-height: 1.2;
    text-align: center;
    white-space: nowrap;

    &-prefix {
      margin-right: 10px;
      color: $owned-media-text-color-base;
      letter-spacing: -3%;
    }

    &-suffix {
      margin-left: 10px;
      color: $owned-media-text-color-base;
      letter-spacing: -3%;
    }

    &-text {
      color: $owned-media-text-color-base;
      letter-spacing: -3%;
    }

    &-accent {
      color: $owned-media-primary-color;
      letter-spacing: -3%;
    }

    &-decoration {
      position: absolute;
      top: 4.69px;
      left: 0;
      z-index: -1;
      width: 240px;
      height: 22.3px;
      background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 240 22.3"><line x1="0" y1="11.15" x2="240" y2="11.15" stroke="%23333333" stroke-width="3"/></svg>');
      background-repeat: no-repeat;
      background-position: left center;
      background-size: 240px 22.3px;
    }
  }

  &__buttons {
    display: flex;
    gap: 25px;
    margin-top: 30px;
  }

  &__button {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 362px;
    height: 92px;
    padding: 0 32px;
    text-decoration: none;
    border-radius: 92px;
    box-shadow: 0 9px 19px rgb(82 134 120 / 13%);
    transition:
      transform 0.3s ease,
      box-shadow 0.3s ease;

    &:hover {
      box-shadow: 0 12px 24px rgb(82 134 120 / 20%);
      transform: translateY(-3px);
    }

    &--outline {
      position: relative;
      background-color: $owned-media-base-color;
      border: 3px solid $owned-media-primary-color;
      transition: all 0.3s ease;

      &::after {
        position: absolute;
        top: 50;
        right: 20px;
        width: 8px;
        height: 8px;
        content: '';
        background-color: transparent;
        border-top: 2px solid $owned-media-primary-color;
        border-right: 2px solid $owned-media-primary-color;
        transform: rotate(45deg);

        &:hover {
          border-top: 2px solid $owned-media-white;
          border-right: 2px solid $owned-media-white;
        }
      }

      .owned-media-cta__button-text {
        font-size: 20px;
        font-weight: 500;
        line-height: 1.35;
        color: $owned-media-primary-color;
        white-space: nowrap;
        transition: color 0.3s ease;
      }

      &:hover {
        background-color: $owned-media-primary-color;
        border-color: $owned-media-primary-color;

        .owned-media-cta__button-text {
          color: $owned-media-white;
        }

        .owned-media-cta__button-arrow path {
          stroke: $owned-media-white;
        }

        &::after {
          border-top: 2px solid $owned-media-white;
          border-right: 2px solid $owned-media-white;
        }
      }
    }

    &--filled {
      background-color: $owned-media-primary-color;
      border: 3px solid $owned-media-primary-color;
      transition: all 0.3s ease;

      &::after {
        position: absolute;
        top: 50;
        right: 20px;
        width: 8px;
        height: 8px;
        content: '';
        background-color: transparent;
        border-top: 2px solid $owned-media-white;
        border-right: 2px solid $owned-media-white;
        transform: rotate(45deg);
      }

      .owned-media-cta__button-text {
        font-size: 20px;
        font-weight: 700;
        line-height: 1.35;
        color: $owned-media-white;
        white-space: nowrap;
        transition: color 0.3s ease;
      }

      &:hover {
        background-color: $owned-media-base-color;
        border-color: $owned-media-primary-color;

        .owned-media-cta__button-text {
          color: $owned-media-primary-color;
        }

        .owned-media-cta__button-arrow path {
          stroke: $owned-media-primary-color;
        }

        &::after {
          border-top: 2px solid $owned-media-primary-color;
          border-right: 2px solid $owned-media-primary-color;
        }
      }
    }

    &-text {
      font-family: $owned-media-font-family-noto;
      font-size: 20px;
      font-weight: 500;
      line-height: 1.35;
      text-align: center;
      letter-spacing: 1%;
    }
  }

  // レスポンシブ対応
  @include owned-media-mobile {
    padding: 30px 15px;

    &__container {
    }

    &__title-wrapper {
      width: clamp(200px, 50vw, 240px);
      height: auto;
    }

    &__title {
      width: auto;
      height: auto;
      font-size: clamp(18px, 5vw, 24px);
      line-height: 1.4;
      white-space: normal;

      &-decoration {
        top: clamp(16px, 4vw, 20px);
        width: clamp(200px, 50vw, 240px);
        height: 18px;
        background-size: clamp(200px, 50vw, 240px) 18px;
      }
    }

    &__buttons {
      flex-direction: column;
      gap: 20px;
      width: 100%;
      margin-top: 40px;
    }

    &__button {
      width: 100%;
      max-width: none;
      height: auto;
      padding: 10px 20px;
      border-radius: 90px;

      &--outline,
      &--filled {
        font-size: 18px;
        border: 2px solid $owned-media-primary-color;

        .owned-media-cta__button-text {
          font-size: 18px;
          font-weight: 400;
          line-height: 1.2;
          white-space: normal;
        }
      }
    }
  }
}
