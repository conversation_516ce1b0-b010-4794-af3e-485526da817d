// ========================================
// 共感パートセクション
// ========================================
.owned-media-empathy {
  @include owned-media-section($owned-media-base-color);

  position: relative;
  width: 100%;

  &__container {
    @include owned-media-container;

    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
  }

  &__header {
    display: flex;
    flex-direction: column;
    gap: $owned-media-spacing-small;
    align-items: center;
    justify-content: center;
    margin-bottom: $owned-media-empathy-header-margin-bottom;
  }

  &__subtitle {
    margin: 0 auto;
    text-align: center;

    &-text {
      position: relative;
      z-index: 2;
      font-family: $owned-media-font-family-noto;
      font-size: 24px;
      font-weight: 700;
      line-height: 1.2;
      color: $owned-media-text-color;
      letter-spacing: -3%;
    }
  }

  &__title {
    display: inline-block;
    text-align: center;

    &-text {
      position: relative;
      z-index: 2;
      display: block;
      font-family: $owned-media-font-family-noto;
      font-size: 30px;
      font-weight: 700;
      line-height: 1.2;
      color: $owned-media-text-color;
      text-align: center;
      letter-spacing: -3%;

      // @include empathy-title-highlight(
      //   $owned-media-bg-color,
      //   $owned-media-empathy-title-highlight-height,
      //   $owned-media-spacing-xs
      // );

      @include owned-media-mobile {
        font-size: 56px;
      }
    }

    &-line1,
    &-line2 {
      display: block;
      text-align: center;

      @include owned-media-mobile {
        font-size: clamp(28px, 5vw, 40px);
        line-height: 1.4;
      }
    }
  }

  &__checklist {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    width: 100%;
    max-width: 860px;
    padding: $owned-media-empathy-checklist-padding;
    border: $owned-media-empathy-checklist-border-width solid $owned-media-mint-light;

    &::after {
      position: absolute;
      top: -$owned-media-empathy-checklist-border-width;
      right: -$owned-media-empathy-checklist-border-width;
      width: $owned-media-empathy-checklist-decoration-size;
      height: $owned-media-empathy-checklist-decoration-size;
      content: '';
      background: linear-gradient(45deg, $owned-media-mint-light 50%, $owned-media-bg-color 50%);
    }
  }

  &__list {
    display: flex;
    flex-direction: column;
    width: 100%;
    list-style: none;
  }

  &__item {
    position: relative;
    display: flex;
    align-items: flex-start;
    padding: $owned-media-empathy-item-padding;

    @include empathy-check-icon(
      $owned-media-empathy-check-icon-size,
      $owned-media-empathy-check-icon-border-width,
      $owned-media-empathy-check-icon-border-radius,
      $owned-media-mint-border
    );
    @include empathy-check-image($owned-media-empathy-check-image-size, $owned-media-spacing-xs);

    &-text {
      flex: 1;
      margin: 0;
      margin-left: $owned-media-empathy-item-text-margin-left;
      word-wrap: break-word;
      overflow-wrap: break-word;

      @include empathy-text-base($owned-media-empathy-item-font-size, 700, $owned-media-text-color);
      @include empathy-item-dash(
        $owned-media-empathy-item-text-margin-left,
        $owned-media-empathy-item-dash-border-width,
        $owned-media-mint-dash
      );
    }

    &-accent {
      font-weight: 900;
      color: $owned-media-primary-color;
    }
  }

  @include media-breakpoint-down(md) {
    padding: 40px 15px;

    &__container {
      padding: 0;
    }

    &__header {
      width: 100%;
      margin-bottom: $owned-media-empathy-mobile-header-margin-bottom;
    }

    &__title {
      padding: 4px;
      background-color: $owned-media-mint-light;
    }

    &__subtitle {
      display: flex;
      align-items: center;
      justify-content: center;
      width: fit-content;
      height: 100%;
      padding: 4px;
      background-color: $owned-media-mint-light;
    }

    &__subtitle-text {
      font-size: $owned-media-empathy-mobile-subtitle-font-size;
      letter-spacing: -0.5px;

      &::before {
        display: none;
      }
    }

    &__title-text {
      font-size: $owned-media-empathy-mobile-title-font-size;
      letter-spacing: -1px;
      white-space: normal;

      // &::after {
      //   background-color: $owned-media-mint-light;
      // }
      &::before {
        display: none;
      }
    }

    &__checklist {
      padding: $owned-media-empathy-mobile-checklist-padding;
      border-width: $owned-media-empathy-mobile-checklist-border-width;

      &::after {
        top: -$owned-media-empathy-mobile-checklist-border-width;
        right: -$owned-media-empathy-mobile-checklist-border-width;
        width: $owned-media-empathy-mobile-checklist-decoration-size;
        height: $owned-media-empathy-mobile-checklist-decoration-size;
      }
    }

    &__item {
      padding: 50px 22px;
      padding-bottom: 32px;
      margin-top: 40px;

      &::before {
        top: -10px;
        bottom: unset;
        left: 50%;
        width: $owned-media-empathy-mobile-check-icon-size;
        height: $owned-media-empathy-mobile-check-icon-size;
        border-width: $owned-media-empathy-mobile-check-icon-border-width;
        transform: translateX(-50%);
      }

      &::after {
        top: -20px;
        bottom: unset;
        left: calc(50% + 10px);
        width: $owned-media-empathy-mobile-check-image-size;
        height: $owned-media-empathy-mobile-check-image-size;
        transform: translateX(-50%);
      }

      &-text {
        margin-left: 0;
        font-size: 20px;
        line-height: 1.5;
        text-align: center;

        &::before {
          left: 50%;
          border-bottom: 4px dashed $owned-media-mint-dash;
          border-bottom-width: $owned-media-empathy-mobile-item-dash-border-width;
          transform: translateX(-50%);
        }
      }
    }
  }
}
