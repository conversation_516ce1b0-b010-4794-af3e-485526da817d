// ==================================================
// ファースト訴求セクション
// ==================================================
.owned-media-first-appeal {
  @include owned-media-section($owned-media-mint-light);

  width: 100%;
  padding-top: 22px;
  padding-bottom: 28px;

  // コンテナ
  &__container {
    @include owned-media-container;

    &::after {
      position: absolute;
      top: -60px;
      left: 50%;
      z-index: 1;
      width: $owned-media-first-appeal-container-decoration-size;
      height: $owned-media-first-appeal-container-decoration-size;
      content: '';
      background-image: url('#{$owned-media-image-path}/first-appeal-decoration.png');
      background-repeat: no-repeat;
      background-size: contain;
      transform: translateX(-50%) rotate(5deg);
    }
  }

  // メッセージエリア
  &__message-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    text-align: center;
  }

  // メインメッセージ
  &__main-message {
    position: relative;
    display: flex;
    flex-direction: row;
    gap: 8px;
    align-items: flex-end;
    justify-content: center;
    margin-bottom: 50px;

    // PC版では全ての要素を横並びに配置
    &-line1,
    &-line2 {
      display: contents;
    }

    &-from {
      margin-right: 8px;
      font-family: $owned-media-font-family-noto;
      font-size: $owned-media-first-appeal-from-font-size;
      font-weight: 900;
      line-height: 0.86;
      color: $owned-media-text-color;
      letter-spacing: -0.03em;
      transform: rotate(-5deg);
    }

    &-connector {
      font-family: $owned-media-font-family-noto;
      font-size: $owned-media-first-appeal-connector-font-size;
      font-weight: 900;
      line-height: 1;
      color: $owned-media-text-color;
      letter-spacing: -0.03em;
    }

    &-to {
      position: relative;
      font-family: $owned-media-font-family-noto;
      font-size: $owned-media-first-appeal-to-font-size;
      font-weight: 900;
      line-height: 1.5;
      color: $owned-media-primary-color;
      letter-spacing: -0.03em;
      transform: rotate(-5deg);

      &::after {
        position: absolute;
        bottom: -50px;
        left: 50%;
        z-index: 2;
        width: 179px;
        height: 62px;
        content: '';
        background-image: url('#{$owned-media-image-path}/graffiti-vector.png');
        background-repeat: no-repeat;
        background-position: center;
        background-size: contain;
        transform: translateX(-50%) rotate(5deg);
      }
    }

    &-suffix {
      margin-left: -22px;
      font-family: $owned-media-font-family-noto;
      font-size: $owned-media-first-appeal-suffix-font-size;
      font-weight: 900;
      line-height: 1.54;
      color: $owned-media-text-color;
      letter-spacing: -0.03em;
    }
  }

  // サブメッセージ
  &__sub-message {
    display: flex;
    flex-direction: column;
    gap: 28px;
    width: 100%;
    text-align: center;
  }

  &__sub-message-text {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin: 0;
    font-family: $owned-media-font-family-noto;
    font-size: $owned-media-first-appeal-sub-message-font-size;
    font-weight: 700;
    line-height: 1.82;
    text-align: center;
    letter-spacing: -0.03em;

    @include owned-media-mobile {
      gap: 8px;
    }
  }

  &__sub-message-line1 {
    display: block;
    color: $owned-media-text-color;

    @include owned-media-mobile {
      word-break: keep-all;
      overflow-wrap: break-word;
    }
  }

  &__sub-message-line2 {
    display: inline-block;
    width: fit-content;
    height: auto;
    padding: $owned-media-first-appeal-sub-message-line2-padding;
    margin: 0 auto;
    font-size: 24px;
    line-height: 1.4;
    color: $owned-media-white;
    text-align: center;
    background-color: $owned-media-primary-color;
    border-radius: $owned-media-first-appeal-sub-message-line2-border-radius;
  }

  // 人物画像グループ
  &__people {
    display: flex;
    flex-wrap: wrap;
    gap: $owned-media-first-appeal-people-gap;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin-bottom: 0;

    &-message {
      order: 3;
      margin: 0;
      font-family: $owned-media-font-family-noto;
      font-size: $owned-media-first-appeal-people-message-font-size;
      font-weight: 900;
      line-height: 1.4;
      color: $owned-media-text-color;
      text-align: center;
      letter-spacing: -2.34px;
      white-space: nowrap;
    }

    &-images {
      display: contents;
    }
  }

  &__person {
    &--1 {
      @include first-appeal-person(1, $owned-media-first-appeal-person-1-width);
    }

    &--2 {
      @include first-appeal-person(2, $owned-media-first-appeal-person-2-width);
    }

    &--3 {
      @include first-appeal-person(3, $owned-media-first-appeal-person-3-width);
    }

    &--4 {
      @include first-appeal-person(4, $owned-media-first-appeal-person-4-width);
    }
  }

  // 装飾画像
  &__decoration {
    position: absolute;
    top: 0;
    right: $owned-media-first-appeal-decoration-right;
    width: $owned-media-first-appeal-decoration-width;
    height: auto;
    object-fit: contain;
  }

  @include media-breakpoint-down(md) {
    padding: $owned-media-first-appeal-mobile-padding;

    &__container {
      padding: 0 $owned-media-spacing-small;

      &::after {
        top: $owned-media-first-appeal-mobile-container-decoration-top;
        width: $owned-media-first-appeal-mobile-container-decoration-size;
        height: $owned-media-first-appeal-mobile-container-decoration-size;
      }
    }

    &__main-message {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 90px;

      &::after {
        bottom: $owned-media-first-appeal-mobile-main-message-decoration-bottom;
        left: 50%;
        width: 100%;
        height: $owned-media-first-appeal-mobile-main-message-decoration-height;
        transform: translateX(-50%);
      }

      &-line1,
      &-line2 {
        display: flex;
        gap: $owned-media-spacing-xs;
        align-items: center;
        justify-content: center;
      }

      &-line1 {
        margin-right: 0;
        margin-bottom: $owned-media-first-appeal-mobile-main-message-line1-margin-bottom;
      }

      &-from {
        margin-right: 0;
        font-size: 50px;
        line-height: 1;
        vertical-align: bottom;
        transform: rotate(-5deg);
      }

      &-connector {
        font-size: $owned-media-first-appeal-mobile-connector-font-size;
      }

      &-to {
        font-size: 50px;
        line-height: 1;
        vertical-align: bottom;
        transform: rotate(-5deg);

        &::after {
          bottom: -70px;
        }
      }

      &-suffix {
        position: static;
        left: unset;
        font-size: $owned-media-first-appeal-mobile-suffix-font-size;
      }
    }

    &__sub-message {
      gap: 12px;
      margin-bottom: 0;
    }

    &__sub-message-text {
      font-size: $owned-media-first-appeal-mobile-sub-message-font-size;
      line-height: 1.6;
      text-align: center;
      word-wrap: break-word;
      white-space: normal;
    }

    &__sub-message-line2 {
      display: inline-block;
      width: 100%;
      height: auto;
      padding: $owned-media-first-appeal-mobile-sub-message-line2-padding;
      margin: 0;
      font-size: $owned-media-first-appeal-mobile-sub-message-line2-font-size;
    }

    &__people {
      flex-direction: column;
      gap: $owned-media-first-appeal-mobile-people-gap;
      align-items: center;
      width: 100%;
      margin-bottom: $owned-media-first-appeal-mobile-people-margin-bottom;

      &-message {
        order: -1;
        margin-bottom: 0;
        font-size: $owned-media-first-appeal-mobile-people-message-font-size;
      }

      &-images {
        display: flex;
        gap: $owned-media-first-appeal-mobile-people-images-gap;
        align-items: flex-end;
        justify-content: center;
        width: 100%;
      }
    }

    &__person {
      &--1,
      &--2,
      &--3,
      &--4 {
        @include first-appeal-mobile-person($owned-media-first-appeal-mobile-person-height);
      }
    }

    &__decoration {
      display: none;
    }
  }
}
