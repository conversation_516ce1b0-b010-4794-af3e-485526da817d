// ==================================================
// mixint定義
// ==================================================

// セクション
@mixin owned-media-section($bg-color: #fff) {
  width: 100%;
  padding: 60px 0 100px;
  background-color: $bg-color;

  @include owned-media-mobile {
    padding: 80px 0 40px;
  }
}

@mixin owned-media-section-bg-gradient(
  $color-1: $owned-media-bg-color,
  $color-2: $owned-media-mint-light,
  $height: 750px
) {
  background: linear-gradient(
    to bottom,
    $color-1 0,
    $color-1 $height,
    $color-2 $height,
    $color-2 100%
  );
}

// セクション見出し
@mixin section-header {
  &__header {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 24px;
    align-items: center;
    margin-bottom: 80px;
    text-align: center;
  }

  &__title {
    font-family: $owned-media-font-family-noto;
    font-size: clamp(52px, 10vw, 60px);
    font-weight: 700;
    color: $owned-media-mint;
  }

  &__subtitle {
    position: relative;
    display: flex;
    gap: 12px;
    align-items: center;
    justify-content: center;

    &::before,
    &::after {
      display: inline-block;
      width: 15px;
      height: 4px;
      content: '';
      background-color: #3ab795;
      border-radius: 2px;
    }

    &::before {
      margin-right: 8px;
    }

    &::after {
      margin-left: 8px;
    }
  }

  &__subtitle-text {
    font-family: $owned-media-font-family-noto;
    font-size: 28px;
    font-weight: 500;
    line-height: 1;
    color: #3ab795;
    letter-spacing: 0.38px;
  }

  @include owned-media-mobile {
    &__header {
      gap: 12px;
      margin-bottom: 48px;
    }

    &__title {
      font-size: 28px;
    }

    &__subtitle {
      gap: 4px;

      &::before {
        height: 2px;
        margin-right: 0;
      }

      &::after {
        height: 2px;
        margin-left: 0;
      }

      &-text {
        font-size: 24px;
      }
    }
  }
}

// セクションコンテナ
@mixin owned-media-container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1112px; // 1080px + padding(16px × 2)
  padding: 0 16px;
  margin: 0 auto;

  @include owned-media-mobile {
    max-width: 100%;
  }
}

// レスポンシブブレークポイント用mixin
@mixin media-breakpoint-down($breakpoint) {
  @if $breakpoint == 'xl' {
    @media (max-width: #{$owned-media-breakpoint-xl}) {
      @content;
    }
  } @else if $breakpoint == 'lg' {
    @media (max-width: #{$owned-media-breakpoint-lg}) {
      @content;
    }
  } @else if $breakpoint == 'md' {
    @media (max-width: #{$owned-media-breakpoint-md}) {
      @content;
    }
  } @else if $breakpoint == 'sm' {
    @media (max-width: #{$owned-media-breakpoint-sm}) {
      @content;
    }
  }
}

// モバイル用mixin
@mixin owned-media-mobile {
  @media (max-width: #{$owned-media-breakpoint-md}) {
    @content;
  }
}

// FVセクション専用ミックスイン
@mixin fv-catch-decoration {
  &::before,
  &::after {
    position: absolute;
    top: 50%;
    width: $owned-media-fv-catch-line-width;
    height: $owned-media-fv-catch-line-height;
    content: '';
    background-color: $owned-media-text-color;
  }

  &::before {
    left: -$owned-media-fv-catch-line-offset;
    transform: translateY(-50%) rotate(-20deg);
  }

  &::after {
    right: -$owned-media-fv-catch-line-offset;
    transform: translateY(-50%) rotate(20deg);
  }
}

@mixin fv-pill-part($bg-color, $text-color, $border-radius, $z-index: 1) {
  position: relative;
  z-index: $z-index;
  color: $text-color;
  background-color: $bg-color;
  border: $owned-media-fv-pill-border-width solid $owned-media-text-color;
  border-radius: $border-radius;
  outline: 1px solid $owned-media-text-color;
  outline-offset: -1px;

  @if $z-index == 2 {
    margin-right: -2px;
    box-shadow: 1px 0 0 0 $owned-media-text-color;
  }
}

@mixin fv-message-text($font-size, $color) {
  font-size: $font-size;
  font-weight: 900;
  line-height: 1;
  color: $color;
  white-space: nowrap;
}

@mixin fv-mobile-catch-decoration {
  &::before,
  &::after {
    height: $owned-media-fv-mobile-catch-line-height;
  }

  &::before {
    left: -$owned-media-fv-mobile-catch-line-offset;
  }

  &::after {
    right: -$owned-media-fv-mobile-catch-line-offset;
  }
}

// First Appealセクション専用ミックスイン
@mixin first-appeal-message-part($font-size, $color, $letter-spacing: -0.03em, $transform: none) {
  font-size: $font-size;
  line-height: 1;
  color: $color;
  letter-spacing: $letter-spacing;
  white-space: nowrap;

  @if $transform != none {
    transform: $transform;
  }
}

@mixin first-appeal-person($order, $width) {
  order: $order;
  width: $width;
  height: $owned-media-first-appeal-person-height;
  object-fit: contain;
  object-position: bottom;
}

@mixin first-appeal-mobile-person($height) {
  order: unset;
  height: $height;
}

// Partner Logosセクション専用ミックスイン
@mixin partner-logo-base {
  object-fit: contain;
  mix-blend-mode: multiply;
}

@mixin partner-logo-pc($max-height) {
  @include partner-logo-base;

  width: auto;
  height: auto;
  max-height: $max-height;
}

@mixin partner-logo-mobile($max-height) {
  @include partner-logo-base;

  flex-shrink: 0;
  width: auto;
  height: auto;
  max-height: $max-height;
}

@mixin scrolling-track-animation($animation-duration) {
  animation: scroll-logos $animation-duration linear infinite;
}

@mixin scrolling-track-animation-pc($animation-duration) {
  animation: scroll-logos-pc $animation-duration linear infinite;
}

// Empathyセクション専用ミックスイン
@mixin empathy-text-base($font-size, $font-weight, $color, $letter-spacing: normal) {
  font-family: $owned-media-font-family-noto;
  font-size: $font-size;
  font-weight: $font-weight;
  line-height: 1;
  color: $color;

  @if $letter-spacing != normal {
    letter-spacing: $letter-spacing;
  }
}

@mixin empathy-title-highlight($bg-color, $height, $bottom-offset) {
  &::after {
    position: absolute;
    bottom: $bottom-offset;
    left: 0;
    z-index: -1;
    width: 100%;
    height: $height;
    content: '';
    background-color: $bg-color;
  }
}

@mixin empathy-check-icon($size, $border-width, $border-radius, $border-color) {
  &::before {
    position: absolute;
    bottom: 50%;
    left: 0;
    z-index: 1;
    width: $size;
    height: $size;
    content: '';
    border: $border-width solid $border-color;
    border-radius: $border-radius;
    transform: translateY(50%);
  }
}

@mixin empathy-check-image($size, $left-offset) {
  &::after {
    position: absolute;
    bottom: 65%;
    left: $left-offset;
    z-index: 2;
    width: $size;
    height: $size;
    content: '';
    background-image: url('#{$owned-media-image-path}/check-icon.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    transform: translateY(50%);
  }
}

@mixin empathy-item-dash($left-offset, $border-width, $color) {
  &::before {
    position: absolute;
    bottom: 0;
    left: $left-offset;
    width: 90%;
    height: 1px;
    content: '';
    border-bottom: $border-width dashed $color;
  }
}

// Discovery セクション専用ミックスイン
@mixin discovery-background-grid {
  background-image:
    linear-gradient(to right, $owned-media-discovery-bg-line-opacity 1px, transparent 1px),
    linear-gradient(to bottom, $owned-media-discovery-bg-line-opacity 1px, transparent 1px);
  background-size: $owned-media-spacing-xxl $owned-media-spacing-xxl;
}

@mixin discovery-diagonal-decoration {
  &::before {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
    width: 100%;
    height: 100%;
    content: '';
    background: linear-gradient(
      135deg,
      $owned-media-discovery-diagonal-bg-color 50%,
      transparent 50%
    );
  }

  &::after {
    position: absolute;
    top: 0;
    left: 50%;
    z-index: 1;
    width: $owned-media-spacing-xxl;
    height: $owned-media-spacing-xxl;
    content: '';
    background-color: $owned-media-base-color;
    transform: $owned-media-discovery-decoration-transform;
  }
}

@mixin discovery-title-text(
  $font-size,
  $color,
  $letter-spacing,
  $weight: 900,
  $line-height: $owned-media-discovery-title-prefix-line-height
) {
  font-family: $owned-media-font-family-noto;
  font-size: $font-size;
  font-weight: $weight;
  line-height: $line-height;
  color: $color;
  letter-spacing: $letter-spacing;
}

@mixin discovery-title-main-accent($width, $height, $right-offset, $transform) {
  &::before {
    position: absolute;
    top: -30px;
    right: $right-offset;
    z-index: 1;
    width: $width;
    height: $height;
    content: '';
    background-image: url('#{$owned-media-image-path}/discovery-accent.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    transform: $transform;
  }
}

@mixin discovery-reason-bubble($width, $height, $bg-image, $transform: none) {
  width: $width;
  height: $height;

  &::before {
    background-image: url('#{$owned-media-image-path}/#{$bg-image}');

    @if $transform != none {
      transform: $transform;
    }
  }
}

@mixin discovery-reason-positioning($top: auto, $right: auto, $bottom: auto, $left: auto) {
  @if $top != auto {
    top: $top;
  }

  @if $right != auto {
    right: $right;
  }

  @if $bottom != auto {
    bottom: $bottom;
  }

  @if $left != auto {
    left: $left;
  }
}

@mixin discovery-reason-text-transform($x-offset: -50%, $y-offset: -50%) {
  top: 50%;
  left: 50%;
  transform: translate($x-offset, $y-offset);
}

@mixin discovery-mobile-bubble {
  width: 100% !important;
  height: $owned-media-discovery-mobile-bubble-height !important;
  padding: $owned-media-discovery-mobile-bubble-padding;
  background-color: $owned-media-discovery-mobile-bg;
  border-radius: $owned-media-discovery-mobile-bubble-border-radius;
  box-shadow: $owned-media-discovery-mobile-bubble-shadow;

  &::before {
    display: none;
  }
}

// Merit セクション専用ミックスイン
// 装飾要素の配置
@mixin merit-decoration($width, $height, $top, $left: auto, $right: auto) {
  position: absolute;
  top: $top;
  width: $width;
  height: $height;

  @if $left != auto {
    left: $left;
  }

  @if $right != auto {
    right: $right;
  }
}

// ブランドテキストのスタイル
@mixin merit-brand-text($height, $padding, $radius, $font-size) {
  display: flex;
  align-items: center;
  justify-content: center;
  height: $height;
  padding: $padding $owned-media-spacing-medium;
  font-family: $owned-media-font-family-noto;
  font-size: $font-size;
  font-weight: 700;
  line-height: 1.2;
  color: $owned-media-white;
  letter-spacing: 0.32px;
  content: '';
  background-color: $owned-media-mint;
  border-radius: $radius;
}

// ブランドテキストの矢印装飾
@mixin merit-brand-arrow($size, $offset, $bg-color: $owned-media-mint) {
  &::before {
    position: absolute;
    bottom: -$offset;
    left: 50%;
    z-index: -1;
    display: block;
    width: $size;
    height: $size;
    content: '';
    background-color: $bg-color;
    transform: translateX(-50%) rotate(45deg) skew(15deg, 15deg);
  }
}

// Noto Sans JPフォントの基本スタイル
@mixin merit-noto-text(
  $font-size,
  $font-weight,
  $color,
  $line-height: 1.2,
  $letter-spacing: normal
) {
  font-family: $owned-media-font-family-noto;
  font-size: $font-size;
  font-weight: $font-weight;
  line-height: $line-height;
  color: $color;

  @if $letter-spacing != normal {
    letter-spacing: $letter-spacing;
  }
}

// SF Proフォントのスタイル
@mixin merit-sf-pro-text($font-size, $font-weight, $color, $letter-spacing: normal) {
  font-family: 'SF Pro', $owned-media-font-family-noto;
  font-size: $font-size;
  font-weight: $font-weight;
  color: $color;

  @if $letter-spacing != normal {
    letter-spacing: $letter-spacing;
  }
}

// メリットアイテムの基本レイアウト
@mixin merit-item-content($gap, $align: center, $justify: space-between) {
  position: relative;
  display: flex;
  gap: $gap;
  align-items: $align;
  justify-content: $justify;
}

// メリットアイテムの画像エリア
@mixin merit-item-image($size, $bg-color: $owned-media-base-color) {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: $size;
  height: $size;
  background-color: $bg-color;
  background-image: url('../images/s-owned-media/subtract-9.svg');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

// メリットアイテムの画像背景装飾
@mixin merit-item-image-bg($top, $left, $max-width, $max-height) {
  &::before {
    position: absolute;
    top: $top;
    left: $left;
    z-index: 0;
    width: 100%;
    max-width: $max-width;
    height: 100%;
    max-height: $max-height;
    content: '';
    background-image: url('../images/s-owned-media/merit-bg.svg');
    background-repeat: no-repeat;
    background-size: 100% 100%;

    @include media-breakpoint-down(md) {
      background-image: url('../images/s-owned-media/merit-bg-sp.svg');
    }
  }
}

// メリットアイテムの反転レイアウト（偶数アイテム用）
@mixin merit-item-reverse {
  .owned-media-merit__item-content {
    flex-direction: row-reverse;
  }

  .owned-media-merit__item-image {
    &::before {
      right: $owned-media-merit-image-bg-offset-left;
      left: unset;
    }
  }
}

// ナンバーアイコンのスタイル
@mixin merit-number-icon($width, $height, $bg-width, $bg-height) {
  position: relative;
  width: $width;
  height: $height;

  &-bg {
    position: absolute;
    top: 0;
    left: 3px;
    width: $bg-width;
    height: $bg-height;
  }

  &-label {
    position: absolute;
    top: $owned-media-merit-number-label-top;
    left: 0;
    width: 100%;
    text-align: center;
    background: $owned-media-merit-gradient;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

// グラデーションテキスト
@mixin merit-gradient-text($font-size, $font-weight, $letter-spacing: normal) {
  font-family: 'SF Pro', $owned-media-font-family-noto;
  font-size: $font-size;
  font-weight: $font-weight;
  text-align: center;
  background: $owned-media-merit-gradient;
  background-clip: text;
  -webkit-text-fill-color: transparent;

  @if $letter-spacing != normal {
    letter-spacing: $letter-spacing;
  }
}

// モバイル向けメリットアイテムのレイアウト
@mixin merit-mobile-item-layout {
  flex-direction: column-reverse;
  gap: $owned-media-merit-mobile-item-content-gap;
  align-items: center;
  width: 100%;
  min-height: auto;
}

// Service セクション専用mixins
// ====================================

// プログレスマーク - デスクトップ版
@mixin service-progress-mark {
  position: absolute;
  top: -20px;
  left: 50%;
  width: $owned-media-service-progress-width;
  height: $owned-media-service-progress-height;
  content: '';
  background-image:
    // ドット7個（デフォルトはグレー）- 前面に表示
    radial-gradient(circle, $owned-media-service-progress-gray 6px, transparent 6px),
    radial-gradient(circle, $owned-media-service-progress-gray 6px, transparent 6px),
    radial-gradient(circle, $owned-media-service-progress-gray 6px, transparent 6px),
    radial-gradient(circle, $owned-media-service-progress-gray 6px, transparent 6px),
    radial-gradient(circle, $owned-media-service-progress-gray 6px, transparent 6px),
    radial-gradient(circle, $owned-media-service-progress-gray 6px, transparent 6px),
    radial-gradient(circle, $owned-media-service-progress-gray 6px, transparent 6px),
    // 背景ライン - 後面に表示
    linear-gradient(
        to right,
        $owned-media-service-progress-gray 0%,
        $owned-media-service-progress-gray 100%
      );
  background-repeat: no-repeat;
  background-position:
    0 0,
    15px 0,
    30px 0,
    44px 0,
    59px 0,
    73px 0,
    88px 0,
    $owned-media-service-progress-line-left $owned-media-service-progress-line-top;
  background-size:
    $owned-media-service-progress-dot-container-size
      $owned-media-service-progress-dot-container-size,
    $owned-media-service-progress-dot-container-size
      $owned-media-service-progress-dot-container-size,
    $owned-media-service-progress-dot-container-size
      $owned-media-service-progress-dot-container-size,
    $owned-media-service-progress-dot-container-size
      $owned-media-service-progress-dot-container-size,
    $owned-media-service-progress-dot-container-size
      $owned-media-service-progress-dot-container-size,
    $owned-media-service-progress-dot-container-size
      $owned-media-service-progress-dot-container-size,
    $owned-media-service-progress-dot-container-size
      $owned-media-service-progress-dot-container-size,
    $owned-media-service-progress-line-width $owned-media-service-progress-line-height;
  transform: translateX(-50%);
}

// プログレスマーク - モバイル版
@mixin service-progress-mark-mobile {
  position: absolute;
  top: 0;
  left: 50%;
  height: $owned-media-service-mobile-progress-height;
  content: '';
  background-image:
    // ドット5個（220px幅に合わせて配置）
    radial-gradient(circle, $owned-media-service-progress-gray 3.75px, transparent 3.75px),
    radial-gradient(circle, $owned-media-service-progress-gray 3.75px, transparent 3.75px),
    radial-gradient(circle, $owned-media-service-progress-gray 3.75px, transparent 3.75px),
    radial-gradient(circle, $owned-media-service-progress-gray 3.75px, transparent 3.75px),
    radial-gradient(circle, $owned-media-service-progress-gray 3.75px, transparent 3.75px),
    // 背景ライン
    linear-gradient(
        to right,
        $owned-media-service-progress-gray 0%,
        $owned-media-service-progress-gray 100%
      );
  background-repeat: no-repeat;
  background-position:
    20px 0,
    65px 0,
    110px 0,
    155px 0,
    200px 0,
    $owned-media-service-mobile-progress-line-left $owned-media-service-mobile-progress-line-top;
  background-size:
    15px 15px,
    15px 15px,
    15px 15px,
    15px 15px,
    15px 15px,
    $owned-media-service-mobile-progress-line-width $owned-media-service-mobile-progress-line-height;
  transform: translateX(-50%);
}

// サービスアイテム番号装飾
@mixin service-number-decoration {
  &::before,
  &::after {
    position: absolute;
    top: 50%;
    display: inline-block;
    width: 10px;
    height: 1px;
    content: '';
    background-color: $owned-media-mint;
    border-radius: $owned-media-service-decoration-border-radius;
    transform: translateY(-50%);
  }

  &::before {
    left: -16px;
    margin-right: $owned-media-service-decoration-margin;
  }

  &::after {
    right: -16px;
    margin-left: $owned-media-service-decoration-margin;
  }
}

// モバイル版サービスアイテム番号装飾
@mixin service-number-decoration-mobile {
  &::before,
  &::after {
    display: inline-block;
    width: $owned-media-service-mobile-decoration-width;
    height: $owned-media-service-mobile-decoration-height;
    content: '';
    background-color: $owned-media-mint;
    border-radius: $owned-media-service-decoration-border-radius;
  }

  &::before {
    margin-right: $owned-media-service-mobile-decoration-margin;
  }

  &::after {
    margin-left: $owned-media-service-mobile-decoration-margin;
  }
}

// サービスアイテム矢印
@mixin service-item-arrow {
  &:not(:last-child) {
    &::after {
      position: absolute;
      bottom: -42px;
      left: 50%;
      width: 140px;
      height: 34px;
      content: '';
      background-image: url('#{$owned-media-image-path}/service-arrow.png');
      background-repeat: no-repeat;
      background-position: center;
      background-size: contain;
      transform: translateX(-50%);
    }
  }
}

// サービスアイコン基本スタイル
@mixin service-icon-base {
  position: absolute;
  top: -70px;
  right: -5%;
  width: $owned-media-service-icon-base-width;
  max-width: 240px;
  height: $owned-media-service-icon-base-height;
  max-height: 150px;
  object-fit: contain;
}

// サービスアイコン個別サイズ
@mixin service-icon-01 {
  @include service-icon-base;

  width: $owned-media-service-icon-01-width;
  height: $owned-media-service-icon-01-height;
}

@mixin service-icon-02 {
  @include service-icon-base;

  width: $owned-media-service-icon-02-width;
  height: $owned-media-service-icon-02-height;
}

@mixin service-icon-03 {
  @include service-icon-base;

  width: $owned-media-service-icon-03-width;
  height: $owned-media-service-icon-03-height;
}

@mixin service-icon-04 {
  @include service-icon-base;

  width: $owned-media-service-icon-04-width;
  height: $owned-media-service-icon-04-height;
}

@mixin service-icon-05 {
  @include service-icon-base;

  width: $owned-media-service-icon-05-width;
  height: $owned-media-service-icon-05-height;
}

@mixin service-icon-06 {
  @include service-icon-base;

  width: $owned-media-service-icon-06-width;
  height: $owned-media-service-icon-06-height;
}

@mixin service-icon-07 {
  @include service-icon-base;

  width: $owned-media-service-icon-07-width;
  height: $owned-media-service-icon-07-height;
}

// サービスアイテムタイトル
@mixin service-item-title {
  font-family: $owned-media-font-family-noto;
  font-size: $owned-media-service-title-font-size;
  font-weight: 700;
}

// サービスアイテム説明文
@mixin service-item-description {
  margin: 0;
  font-family: $owned-media-font-family-noto;
  font-size: $owned-media-service-description-font-size;
  font-weight: 400;
  line-height: $owned-media-service-description-line-height;
  color: $owned-media-text-color;
  letter-spacing: $owned-media-service-description-letter-spacing;
}

// Success セクション専用mixins
// ====================================

// 成功セクションの背景グリッド
@mixin success-background-grid {
  background-image:
    linear-gradient(to right, rgb(97 106 109 / 15%) 1px, transparent 1px),
    linear-gradient(to bottom, rgb(97 106 109 / 15%) 1px, transparent 1px);
  background-size: $owned-media-spacing-xxl $owned-media-spacing-xxl;
}

// 成功セクションのタイトル背景装飾
@mixin success-title-wrapper-bg {
  &::before {
    position: absolute;
    top: $owned-media-success-title-bg-top;
    left: 50%;
    z-index: $owned-media-success-title-wrapper-z-index;
    width: $owned-media-success-title-bg-width;
    max-width: 100%;
    height: 100%;
    content: '';
    background-image: url('#{$owned-media-image-path}/success-title-bg.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    transform: translateX(-50%);
  }

  @include media-breakpoint-down(md) {
    &::before {
      background-image: url('#{$owned-media-image-path}/success-title-bg-sp.png');
    }
  }
}

// 成功セクションのタイトルテキスト
@mixin success-title-text($font-size, $font-weight, $line-height, $color, $letter-spacing: normal) {
  position: relative;
  z-index: $owned-media-success-title-z-index;
  margin: 0;
  font-family: $owned-media-font-family-noto;
  font-size: $font-size;
  font-weight: $font-weight;
  line-height: $line-height;
  color: $color;
  text-align: center;

  @if $letter-spacing != normal {
    letter-spacing: $letter-spacing;
  }
}

// 成功セクションのタイトルマーク
@mixin success-title-mark {
  position: absolute;
  top: -20px;
  right: 0;
  bottom: $owned-media-success-title-mark-bottom;
  font-size: $owned-media-success-title-mark-font-size;
  font-weight: $owned-media-success-title-mark-weight;
  color: $owned-media-text-color;
  transform: $owned-media-success-title-mark-transform;
}

// 成功セクションの説明テキスト
@mixin success-description-text($font-size, $font-weight, $line-height, $color) {
  position: relative;
  z-index: 1;
  margin: 0;
  font-family: $owned-media-font-family-noto;
  font-size: $font-size;
  font-weight: $font-weight;
  line-height: $line-height;
  color: $color;
  white-space: nowrap;
}

// 成功セクションのマーカー装飾
@mixin success-marker($bg-color: $owned-media-success-marker) {
  &.marker {
    background-color: $bg-color;
  }
}

// 成功セクションのモバイル用マーカー装飾
@mixin success-marker-mobile($bg-color: $owned-media-success-marker) {
  &.marker {
    padding: $owned-media-success-marker-padding;
    background-color: $bg-color;
    border-radius: $owned-media-success-marker-border-radius;
  }
}

// System Support セクション専用mixins
// ====================================

// システムサポートの視覚要素テキスト
@mixin system-support-visual-text {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: fit-content;
  height: auto;
  padding: 22px 56px;
  font-size: $owned-media-system-support-text-font-size;
  font-weight: $owned-media-system-support-text-weight;
  line-height: 1;
  color: $owned-media-white;
  background-color: $owned-media-mint-border;
  border-radius: $owned-media-system-support-text-border-radius;
}

// システムサポートの画像エリア
@mixin system-support-visual-image {
  display: flex;
  align-items: center;
  justify-content: center;
  width: fit-content;
  height: fit-content;
  padding: 38px 60px;
  background-color: $owned-media-white;
  border-radius: $owned-media-system-support-image-border-radius;
  box-shadow: $owned-media-system-support-shadow;
}

// システムサポートの矢印装飾
@mixin system-support-arrow {
  &::after {
    position: absolute;
    top: 35%;
    right: -42%;
    width: 230px;
    height: 110px;
    content: '';
    background-image: url('#{$owned-media-image-path}/support-arrow.png');
    background-repeat: no-repeat;
    background-size: contain;
  }
}

// システムサポートのメッセージバブル
@mixin system-support-message-bubble {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 116px;
  padding: 22px 56px;
  background-color: $owned-media-white;
  border-radius: 18px;
  box-shadow: $owned-media-system-support-shadow;

  &::before {
    position: absolute;
    top: -20px;
    left: 46.5%;
    z-index: 0;
    width: 40px;
    height: 40px;
    content: '';
    background-color: $owned-media-white;
    transform: rotate(45deg) skew(20deg, 20deg);
  }
}

// システムサポートのメッセージアクセント
@mixin system-support-message-accent {
  position: relative;
  z-index: 2;
  font-size: 28px;
  font-weight: $owned-media-system-support-message-accent-weight;
  color: $owned-media-primary-color;
  letter-spacing: $owned-media-system-support-message-accent-letter-spacing;
  -webkit-text-stroke: $owned-media-system-support-message-accent-stroke $owned-media-primary-color;

  // &::before {
  //   position: absolute;
  //   bottom: 0;
  //   left: 0;
  //   z-index: -1;
  //   width: 100%;
  //   height: $owned-media-system-support-message-accent-highlight-height;
  //   content: '';
  //   background-color: $owned-media-success-marker;
  // }
}

// システムサポートのメッセージテキスト
@mixin system-support-message-text {
  font-size: 28px;
  font-weight: $owned-media-system-support-message-text-weight;
  color: $owned-media-text-color;
  letter-spacing: $owned-media-system-support-message-text-letter-spacing;
}

// FAQ セクション専用mixins
// ====================================

// FAQテキストの基本レイアウト
@mixin faq-text-base {
  display: $owned-media-faq-text-display;
  gap: $owned-media-faq-text-gap;
  align-items: $owned-media-faq-text-align;
  font-family: $owned-media-font-family-noto;
}

// FAQアイコンの基本スタイル
@mixin faq-icon($size, $font-size, $border-radius, $content, $bg-color) {
  &::before {
    display: $owned-media-faq-icon-display;
    flex-shrink: 0;
    align-items: $owned-media-faq-icon-align;
    justify-content: $owned-media-faq-icon-justify;
    width: $size;
    height: $size;
    font-size: $font-size;
    font-weight: 900;
    line-height: 1;
    color: $owned-media-white;
    content: $content;
    background-color: $bg-color;
    border-radius: $border-radius;
  }
}

// FAQ質問スタイル
@mixin faq-question-style {
  @include faq-text-base;
  @include faq-icon(
    $owned-media-faq-question-icon-size,
    $owned-media-faq-question-icon-font-size,
    $owned-media-faq-question-icon-border-radius,
    'Q',
    $owned-media-mint
  );
}

// FAQ回答スタイル
@mixin faq-answer-style {
  @include faq-text-base;
  @include faq-icon(
    $owned-media-faq-question-icon-size,
    $owned-media-faq-question-icon-font-size,
    $owned-media-faq-question-icon-border-radius,
    'A',
    $owned-media-faq-answer-icon-color
  );
}

// FAQテキストのスタイル
@mixin faq-text-content(
  $font-size,
  $font-weight,
  $color,
  $line-height: $owned-media-faq-text-line-height
) {
  font-size: $font-size;
  font-weight: $font-weight;
  line-height: $line-height;
  color: $color;
}
