// ////////////////////////////
// ご支援体制
// ////////////////////////////
.owned-media-system-support {
  @include owned-media-section($owned-media-bg-color);
  @include owned-media-section-bg-gradient($owned-media-bg-color, $owned-media-mint-light, 550px);

  &__container {
    @include owned-media-container;
  }

  @include section-header;

  &__visual {
    display: flex;
    flex-direction: column;
    gap: $owned-media-system-support-visual-gap;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;

    .visual-container {
      display: flex;
      align-items: stretch;
      justify-content: space-between;
      width: 100%;
      height: 100%;
    }

    .visual-left {
      display: flex;
      flex-direction: column;
      gap: 50px;
      align-items: center;
      justify-content: space-between;
      width: 45%;

      // min-height: 300px;

      &__text {
        @include system-support-visual-text;
      }

      &__image {
        position: relative;
        flex: 1;
        gap: $owned-media-spacing-20;
        padding-right: 60px;

        @include system-support-visual-image;
        @include system-support-arrow;

        img {
          flex-shrink: 0;
          object-fit: cover;

          &:first-child {
            width: 100%;
            max-width: 245px;
            height: auto;
          }

          &:last-child {
            width: 100%;
            max-width: 166px;
            height: auto;
          }
        }
      }
    }

    .visual-right {
      display: flex;
      flex-direction: column;
      gap: 50px;
      align-items: center;
      justify-content: space-between;
      width: 48%;
      min-height: 300px;

      &__text {
        @include system-support-visual-text;
      }

      &__image {
        @include system-support-visual-image;

        flex: 1;
        width: 100%;

        img {
          flex-shrink: 0;
          width: 100%;
          max-width: 184px;
          height: auto;
          object-fit: cover;
        }
      }
    }
  }

  &__team-image {
    width: $owned-media-system-support-team-image-width;
    height: $owned-media-system-support-team-image-height;
    object-fit: contain;
  }

  &__message {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    margin-top: 22px;
  }

  &__message-bubble {
    @include system-support-message-bubble;
  }

  &__message-accent {
    @include system-support-message-accent;
  }

  &__message-text {
    @include system-support-message-text;
  }

  // レスポンシブ対応
  @include media-breakpoint-down(md) {
    padding: $owned-media-section-padding-mobile 0 $owned-media-spacing-xl;

    @include owned-media-section-bg-gradient($owned-media-bg-color, $owned-media-mint-light, 400px);

    &__container {
      padding: 0 $owned-media-spacing-20;
    }

    &__header {
      margin-bottom: $owned-media-section-padding-desktop;
    }

    &__visual {
      gap: $owned-media-system-support-mobile-visual-gap;

      .visual-container {
        flex-direction: column-reverse;
        gap: $owned-media-system-support-mobile-container-gap;
        align-items: center;
      }

      .visual-left {
        position: relative;
        gap: 0;
        width: 100%;
        min-height: auto;

        &__text {
          position: absolute;
          bottom: $owned-media-system-support-mobile-text-bottom;
          left: 50%;
          z-index: 2;
          width: $owned-media-system-support-mobile-text-width;
          height: $owned-media-system-support-mobile-text-height;
          padding: $owned-media-system-support-mobile-text-padding-v
            $owned-media-system-support-mobile-text-padding-h;
          font-size: $owned-media-system-support-mobile-text-font-size;
          font-weight: $owned-media-system-support-text-weight;
          color: $owned-media-white;
          background-color: $owned-media-mint-border;
          border-radius: $owned-media-system-support-mobile-text-border-radius;
          transform: translateX(-50%);
        }

        &__image {
          flex-direction: column-reverse;
          gap: $owned-media-spacing-20;
          width: 100%;
          padding: $owned-media-system-support-mobile-image-padding-v
            $owned-media-system-support-mobile-image-padding-h;
          border-radius: $owned-media-system-support-mobile-image-border-radius;

          // 矢印を非表示
          &::after {
            top: $owned-media-system-support-mobile-arrow-top;
            left: 50%;
            z-index: 1;
            width: $owned-media-system-support-mobile-arrow-width;
            height: $owned-media-system-support-mobile-arrow-height;
            content: '';
            transform: $owned-media-system-support-mobile-arrow-transform;
          }

          img {
            &:first-child {
              width: 100%;
              max-width: 295px;
              height: auto;
              object-fit: contain;
            }

            &:last-child {
              width: 100%;
              max-width: 236px;
              height: auto;
              object-fit: contain;
            }
          }
        }
      }

      .visual-right {
        position: relative;
        gap: 0;
        width: 100%;
        min-height: auto;

        &__text {
          position: absolute;
          top: -25px;
          left: 50%;
          z-index: 2;
          width: 70%;
          height: 50px;
          padding: $owned-media-system-support-mobile-text-padding-v
            $owned-media-system-support-mobile-text-padding-h;
          font-size: clamp(24px, 6vw, 32px);
          border-radius: $owned-media-system-support-mobile-text-border-radius;
          transform: translateX(-50%);
        }

        &__image {
          width: 100%;
          padding: $owned-media-system-support-mobile-image-padding-v
            $owned-media-system-support-mobile-image-padding-h;
          border-radius: $owned-media-system-support-mobile-image-border-radius;
          box-shadow: $owned-media-system-support-mobile-shadow;

          img {
            width: 100%;
            max-width: 236px;
            height: auto;
            object-fit: contain;
          }
        }
      }
    }

    &__team-image {
      width: 100%;
      max-width: 100%;
      height: auto;
      margin: $owned-media-spacing-xl 0;
    }

    &__message {
      margin-top: $owned-media-spacing-xl;
    }

    &__message-bubble {
      z-index: 3;
      width: 100%;
      height: auto;
      padding: $owned-media-system-support-mobile-message-padding-v
        $owned-media-system-support-mobile-message-padding-h;
      border-radius: $owned-media-system-support-mobile-message-border-radius;

      &::before {
        top: 0;
        left: 60%;
        z-index: -1;
        width: 40px;
        height: 40px;
        transform: translateX(-50%) rotate(45deg) skew(30deg, 30deg);
      }
    }

    &__message-accent {
      z-index: 3;
      font-size: 24px;
      font-weight: 900;
      text-align: center;
      background-color: $owned-media-success-marker;
      -webkit-text-stroke: unset;

      &::before {
        display: none;
      }
    }

    &__message-text {
      z-index: 3;
      font-size: 24px;
      font-weight: 500;
    }
  }
}
