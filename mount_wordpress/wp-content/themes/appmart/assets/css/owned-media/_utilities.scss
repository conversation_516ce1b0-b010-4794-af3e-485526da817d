// ==================================================
// ユーティリティクラス
// ==================================================

// 表示制御
.u-pc-only {
  @include owned-media-mobile {
    display: none !important;
  }
}

.u-sp-only {
  display: none !important;

  @include owned-media-mobile {
    display: block !important;
  }

  &.inline {
    @include owned-media-mobile {
      display: inline-flex !important;
    }
  }
}

// テキスト装飾
.u-underline {
  position: relative;
  display: inline-block;

  &::before {
    position: absolute;
    bottom: -4px;
    left: 0;
    z-index: -1;
    width: 100%;
    height: 14px;
    content: '';
    background-color: #fff54b;
    border-radius: 7px;
  }

  &--mint {
    &::before {
      bottom: -4px;
      height: 18px;
      background-color: #b1e2d5;
      border-radius: unset;
    }
  }

  &--none {
    &::before {
      display: none;
    }
  }
}

// レイアウト
.u-flex {
  display: flex;
}

.u-flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.u-flex-column {
  display: flex;
  flex-direction: column;
}

// スペーシング
.u-mt-1 {
  margin-top: 8px;
}

.u-mt-2 {
  margin-top: 16px;
}

.u-mt-3 {
  margin-top: 24px;
}

.u-mt-4 {
  margin-top: 32px;
}

.u-mt-5 {
  margin-top: 40px;
}

.u-mb-1 {
  margin-bottom: 8px;
}

.u-mb-2 {
  margin-bottom: 16px;
}

.u-mb-3 {
  margin-bottom: 24px;
}

.u-mb-4 {
  margin-bottom: 32px;
}

.u-mb-5 {
  margin-bottom: 40px;
}

// テキスト
.u-text-center {
  text-align: center;
}

.u-text-left {
  text-align: left;
}

.u-text-right {
  text-align: right;
}

.u-font-weight-bold {
  font-weight: 700;
}

.u-font-weight-normal {
  font-weight: 400;
}
