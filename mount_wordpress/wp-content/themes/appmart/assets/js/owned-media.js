/**
 * オウンドメディアサービスページ用JavaScript（シンプルCSS実装）
 * CSS transformベースの無限スクロール
 */

(function () {
  'use strict';

  // DOM読み込み完了後に実行
  document.addEventListener('DOMContentLoaded', function () {
    // プログレスマーク初期化
    initProgressMarks();
    
    // スムーススクロール初期化
    initSmoothScroll();
    
    // 動的フォーム配置初期化
    initDynamicFormPlacement();
    
    initPartnerLogosScroll();
  });

  /**
   * プログレスマーク初期化
   */
  function initProgressMarks() {
    const serviceItems = document.querySelectorAll(
      '.owned-media-service__item-number[data-progress]',
    );

    serviceItems.forEach(function (item) {
      const progressValue = parseInt(item.getAttribute('data-progress'));
      updateProgressMark(item, progressValue);
    });
  }

  /**
   * プログレスマークを更新
   * @param {Element} element - 番号要素
   * @param {number} activeStep - アクティブなステップ（1-7）
   */
  function updateProgressMark(element, activeStep) {
    // 7個のドットの背景画像を生成
    const dots = [];
    const backgroundLine = 'linear-gradient(to right, #d9d9d9 0%, #d9d9d9 100%)';

    for (let i = 1; i <= 7; i++) {
      if (i === activeStep) {
        // アクティブなドット：白い塗りつぶし + 緑の外周線（2px）
        dots.push(
          'radial-gradient(circle, #ffffff 4px, #ffffff 4px, #7dc8b6 4px, #7dc8b6 6px, transparent 6px)',
        );
      } else {
        // 非アクティブなドット：グレーの塗りつぶし
        dots.push('radial-gradient(circle, #d9d9d9 6px, transparent 6px)');
      }
    }

    // ドットを前面に、背景ラインを後面に配置
    const backgroundImages = [...dots, backgroundLine];

    // 背景位置を設定（ドット7個 + 背景ライン1個）
    const backgroundPositions = [
      '0 0', // ドット1
      '15px 0', // ドット2
      '30px 0', // ドット3
      '44px 0', // ドット4
      '59px 0', // ドット5
      '73px 0', // ドット6
      '88px 0', // ドット7
      '11px 11px', // 背景ライン
    ];

    // 背景サイズを設定
    const backgroundSizes = [
      '24px 24px', // ドット1
      '24px 24px', // ドット2
      '24px 24px', // ドット3
      '24px 24px', // ドット4
      '24px 24px', // ドット5
      '24px 24px', // ドット6
      '24px 24px', // ドット7
      '88px 3px', // 背景ライン
    ];

    // 擬似要素のスタイルを動的に設定
    const style = document.createElement('style');
    const uniqueClass = 'progress-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    element.classList.add(uniqueClass);

    style.textContent = `
      .${uniqueClass}::before {
        background-image: ${backgroundImages.join(', ')};
        background-position: ${backgroundPositions.join(', ')};
        background-size: ${backgroundSizes.join(', ')};
      }
    `;

    document.head.appendChild(style);
  }

  /**
   * 提携企業ロゴの無限スクロール初期化（CSS transform使用）
   */
  function initPartnerLogosScroll() {
    const logoRows = document.querySelectorAll('.owned-media-partner-logos__row');

    logoRows.forEach(function (row, rowIndex) {
      const track = row.querySelector('.owned-media-partner-logos__track');
      if (!track) {
        return;
      }

      const rowType = track.getAttribute('data-row');

      // 元の画像を取得
      const originalImages = Array.from(track.children);

      if (originalImages.length === 0) {
        return;
      }

      // 無限スクロール用に画像を3セット分作成
      for (let i = 0; i < 2; i++) {
        originalImages.forEach((img) => {
          const clonedImg = img.cloneNode(true);
          track.appendChild(clonedImg);
        });
      }

      // CSS設定
      track.style.display = 'flex';
      track.style.gap = '40px';
      track.style.alignItems = 'center';
      track.style.width = 'max-content';

      // 実際の画像幅を計算（画像読み込み後）
      let totalWidth = 0;
      let currentTranslate = 0;
      const speed = rowType === 'top' ? 0.7 : 0.6;
      let animationStarted = false;

      // 画像の読み込み完了を待って実際のサイズを取得
      const calculateActualWidth = () => {
        // より正確な計算: 最初の画像セットと2番目の画像セットの位置差を測定
        const firstImage = track.children[0];
        const secondSetFirstImage = track.children[originalImages.length];

        if (firstImage && secondSetFirstImage) {
          const firstRect = firstImage.getBoundingClientRect();
          const secondRect = secondSetFirstImage.getBoundingClientRect();

          // 2つの同じ位置の画像間の距離 = 1セット分の正確な幅
          totalWidth = Math.abs(secondRect.left - firstRect.left);
        } else {
          // フォールバック: trackの幅÷3
          const trackRect = track.getBoundingClientRect();
          totalWidth = trackRect.width / 3;
        }

        // アニメーション開始
        if (!animationStarted) {
          animationStarted = true;
          animate();
        }
      };

      // 画像の読み込み状況をチェック
      let loadedImages = 0;
      const totalImages = originalImages.length;

      if (totalImages === 0) {
        return;
      }

      // 画像読み込み完了時の処理
      const onImageLoad = () => {
        loadedImages++;
        if (loadedImages === totalImages) {
          // 少し遅延してからサイズ計算（レンダリング完了を待つ）
          setTimeout(calculateActualWidth, 100);
        }
      };

      // 各画像の読み込み状況をチェック
      originalImages.forEach((img) => {
        if (img.complete) {
          loadedImages++;
        } else {
          img.addEventListener('load', onImageLoad);
          img.addEventListener('error', onImageLoad); // エラーでも処理を続行
        }
      });

      // すべて読み込み済みの場合は即座に実行
      if (loadedImages === totalImages) {
        setTimeout(calculateActualWidth, 100);
      }

      // タイムアウト処理（3秒後に強制実行）
      setTimeout(() => {
        if (!animationStarted) {
          calculateActualWidth();
        }
      }, 3000);

      function animate() {
        // 1セット分スクロールしたらリセット
        if (Math.abs(currentTranslate) >= totalWidth) {
          currentTranslate = 0;
        }

        // スクロール位置を更新
        currentTranslate -= speed;

        // transform適用
        track.style.transform = `translateX(${currentTranslate}px)`;

        requestAnimationFrame(animate);
      }
    });
  }

  /**
   * スムーススクロール初期化
   */
  function initSmoothScroll() {
    // CTAボタンのセレクターを更新
    const ctaButtons = document.querySelectorAll(
      '.owned-media-cta__button, .owned-media__cta-form-wrapper, .owned-media__fv-form-wrapper',
    );

    ctaButtons.forEach(function (button) {
      button.addEventListener('click', function (e) {
        const href = this.getAttribute('href');
        
        // 外部URLまたは相対URLの場合は通常通り遷移
        if (href && href !== '#' && !href.startsWith('#')) {
          // 通常のリンク動作を維持（preventDefault()を呼ばない）
          return;
        }
        
        // ページ内リンクまたは空のhrefの場合のみスムーススクロールを実行
        // フォームセクションを検索（複数の可能性のあるセレクターを試す）
        let targetSection = null;
        
        // 候補となるセクションを優先順位で検索
        const sectionSelectors = [
          '#owned-media-form',           // FVセクションのフォーム
          '.section_contact',            // メインのお問い合わせセクション
          '.section__contact',           // 別の可能性のあるクラス名
          '[id*="contact"]',             // IDにcontactを含むセクション
          '[class*="contact"]'           // クラスにcontactを含むセクション
        ];
        
        for (const selector of sectionSelectors) {
          targetSection = document.querySelector(selector);
          if (targetSection) {
            break;
          }
        }
        
        if (targetSection) {
          e.preventDefault();
          
          // より滑らかなスクロール設定
          targetSection.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'nearest'
          });
          
          // フォーカス管理（アクセシビリティ向上）
          setTimeout(() => {
            const firstInput = targetSection.querySelector('input, textarea, select');
            if (firstInput) {
              firstInput.focus();
            }
          }, 500); // スクロール完了後にフォーカス
        }
      });
    });
    
    // アンカーリンクへのスムーズスクロールも追加
    initAnchorScroll();
  }
  
  /**
   * アンカーリンクのスムーズスクロール初期化
   */
  function initAnchorScroll() {
    // ページ内アンカーリンクにスムーズスクロールを適用
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    
    anchorLinks.forEach(function (link) {
      link.addEventListener('click', function (e) {
        const href = this.getAttribute('href');
        if (href === '#' || href === '#top') {
          // トップへスクロール
          e.preventDefault();
          window.scrollTo({
            top: 0,
            behavior: 'smooth'
          });
        } else {
          // 対象セクションへスクロール
          const targetId = href.substring(1);
          const targetElement = document.getElementById(targetId);
          
          if (targetElement) {
            e.preventDefault();
            targetElement.scrollIntoView({
              behavior: 'smooth',
              block: 'start',
              inline: 'nearest'
            });
          }
        }
      });
    });
  }

  /**
   * 動的フォーム配置初期化
   */
  function initDynamicFormPlacement() {
    // フォームが配置された後に実行
    setTimeout(function () {
      const originalFormContainer = document.querySelector('.section__contact__cont');
      const topFormContainer = document.getElementById('top-form-container');

      if (!originalFormContainer || !topFormContainer) return;

      // フォーム要素を探す（BowNowフォームはscriptタグまたはiframeとして挿入される）
      let formElement = null;

      // scriptタグ、iframe、その他のフォーム要素を探す
      const possibleFormElements = originalFormContainer.querySelectorAll(
        'script, iframe, form, div[id*="bownow"], div[class*="bownow"]',
      );

      if (possibleFormElements.length > 0) {
        // フォーム関連要素をすべて取得
        formElement = document.createElement('div');
        formElement.className = 'bownow-form-wrapper';

        // 元のコンテナの全内容を保存
        formElement.innerHTML = originalFormContainer.innerHTML;
      }

      if (!formElement) return;

      // フォームの現在位置を追跡
      let formIsInTop = false;
      // フォーム移動を一時停止するフラグ
      let formMovementPaused = false;

      // フォームを移動する関数（改善版）
      function moveFormTo(targetContainer, sourceContainer) {
        // フォーム移動が一時停止中の場合は何もしない
        if (formMovementPaused) {
          return;
        }

        // コンテナの存在確認
        if (!targetContainer || !sourceContainer) {
          return;
        }

        // フォーム送信中の場合は移動を避ける（追加の安全措置）
        const isFormSubmitting =
          sourceContainer.querySelector('.show-page-btn .btn[disabled]') !== null ||
          targetContainer.querySelector('.show-page-btn .btn[disabled]') !== null;
        if (isFormSubmitting) {
          return;
        }

        // DOM要素を安全に移動（イベントリスナーを保持）
        try {
          while (sourceContainer.firstChild) {
            targetContainer.appendChild(sourceContainer.firstChild);
          }
        } catch (error) {
          // エラーは静かに処理
        }
      }

      // 初期配置（ページ上部に配置）
      moveFormTo(topFormContainer, originalFormContainer);
      formIsInTop = true;

      // スクロールイベントハンドラー（改善版）
      const handleScroll = debounce(function () {
        // フォーム移動が一時停止中の場合は何もしない
        if (formMovementPaused) {
          return;
        }

        // フォーム送信中かどうかをチェック（追加の安全措置）
        const isFormSubmitting = document.querySelector('.show-page-btn .btn[disabled]') !== null;
        if (isFormSubmitting) {
          return;
        }

        const scrollPosition = window.scrollY || document.documentElement.scrollTop;
        const documentHeight = document.documentElement.scrollHeight;
        const halfwayPoint = documentHeight / 2;

        // スクロール位置が画面の半分を超えたら
        if (scrollPosition > halfwayPoint && formIsInTop) {
          // フォームを元の位置（下部）に戻す
          moveFormTo(originalFormContainer, topFormContainer);
          formIsInTop = false;
        } else if (scrollPosition <= halfwayPoint && !formIsInTop) {
          // フォームを上部に移動
          moveFormTo(topFormContainer, originalFormContainer);
          formIsInTop = true;
        }
      }, 150);

      // スクロールイベントをリッスン
      window.addEventListener('scroll', handleScroll);

      // 初期スクロール位置をチェック
      handleScroll();
    }, 1000); // BowNowフォームの読み込みを待つ
  }

  /**
   * デバウンス関数
   */
  function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = function() {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }
})();
